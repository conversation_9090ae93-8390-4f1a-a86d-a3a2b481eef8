#include "MeasureDataHandler.h"
#include <QFile>
#include <QDataStream>
#include <QBuffer>
#include <QDateTime>
#include <QMessageBox>
#include <QDebug>
#include <QFileDialog>
#include <QtConcurrent/QtConcurrent> // 添加QtConcurrent头文件

qint64 MeasureDataHandler::calculateFileSize() {
    qint64 size = 0;
    if (fixedSize <= 0) {
        fixedSize = 0;

        // 计算基本类型成员变量的大小
        fixedSize += sizeof(saving);
        fixedSize += sizeof(errorFlag);
        fixedSize += ident.size() * sizeof(QChar);
        fixedSize += softwareVersion.size() * sizeof(QChar);
        fixedSize += hardwareVersion.size() * sizeof(QChar);
        fixedSize += fileTime.size() * sizeof(QChar);
        fixedSize += fileChangeTime.size() * sizeof(QChar);
        fixedSize += comment.size() * sizeof(QChar);
        fixedSize += measurementMode.size() * sizeof(QChar);
        fixedSize += sizeof(displayLinLog);
        fixedSize += sizeof(displayWaveAxisLower);
        fixedSize += sizeof(displayWaveAxisUpper);
        fixedSize += sizeof(displayTimeAxisLower);
        fixedSize += sizeof(displayTimeAxisUpper);
        fixedSize += sizeof(displayCountAxisLower);
        fixedSize += sizeof(displayCountAxisUpper);
        fixedSize += sizeof(monoValid);
        fixedSize += sizeof(monoGrating);
        fixedSize += sizeof(monoGroove);
        fixedSize += sizeof(monoBlaze);
        fixedSize += sizeof(monoWave);
        fixedSize += sizeof(monoPort);
        fixedSize += sizeof(monoShutter);
        fixedSize += monoFilter.size() * sizeof(QChar);
        fixedSize += sizeof(tdcResolution);
        fixedSize += sizeof(waveChannels);
        fixedSize += sizeof(timeChannels);
        fixedSize += sizeof(stopCondition);
        fixedSize += sizeof(maxMeasurementTiming);
        fixedSize += sizeof(maxNumPhotons);
        fixedSize += sizeof(startTime);
        fixedSize += sizeof(endTime);
        fixedSize += filePath.size() * sizeof(QChar);

        // 计算容器类型成员变量的大小
        fixedSize += tdcResolutionArray.size() * sizeof(float);
        fixedSize += waveAxisArray.size() * sizeof(float);
        fixedSize += timeAxisArray.size() * sizeof(float);
        fixedSize += dataArrayTotal.size() * waveChannels * timeChannels * sizeof(int);
    }
    size += fixedSize;
    size += dataArraySize * (waveChannels * timeChannels * sizeof(int) + sizeof(quint16) + sizeof(quint32));

    return size;
}
MeasureDataHandler::MeasureDataHandler(QObject *parent)
    : QObject(parent)
{
    fixedSize = -1;
    // 初始化默认值
    saving = false;
    errorFlag = 1;
    ident = "128-TCSPC";
    softwareVersion = "1.0";
    hardwareVersion = "1.0";
    fileTime = QDateTime::currentDateTime().toString("yyyy.MM.dd,hh:mm:ss");
    fileChangeTime = "";
    comment = "";
    measurementMode = "H1";
    displayLinLog = 1;
    displayWaveAxisLower = 0;
    displayWaveAxisUpper = 0;
    displayTimeAxisLower = 0;
    displayTimeAxisUpper = 0;
    displayCountAxisLower = 0;
    displayCountAxisUpper = 0;

    monoValid = 1;
    monoGrating = 1;
    monoGroove = 1800;
    monoBlaze = 500;
    monoWave = 621.3f;
    monoPort = 1;
    monoShutter = 1;
    monoFilter = "5:496LP";
    tdcResolution = 10.0f;
    waveChannels = 128;
    timeChannels = 4096;
    stopCondition = 3;
    maxMeasurementTiming = 20;
    maxNumPhotons = 1000;
    startTime = 0;
    endTime = 0;

    // 初始化vector和QList
    tdcResolutionArray.resize(waveChannels, 0.0f);
    waveAxisArray.resize(waveChannels, 0.0f);
    timeAxisArray.resize(timeChannels, 0.0f);
    dataArrayTotal.resize(waveChannels, std::vector<int>(timeChannels, 0));
    dataArray.clear();
    filePath = ""; // 初始化文件路径为空
    dataArraySize = 0; // 初始化dataArraySize为0

    // 初始化新增成员变量
    m_lastSaveTime = QDateTime::currentMSecsSinceEpoch();

    // 创建并启动定时保存计时器
    m_saveTimer = new QTimer(this);
    connect(m_saveTimer, &QTimer::timeout, this, &MeasureDataHandler::checkAndSaveData);
    m_saveTimer->start(30000); // 每30秒检查一次

    // 估算单个数据帧大小
    m_estimatedFrameSize = sizeof(DataFrame) + (waveChannels * timeChannels * sizeof(int));

    // 直接计算最佳FrameInFile值，不再依赖配置文件
    calculateOptimalFrameInFile();
}

void MeasureDataHandler::createSnapshot(int version)
{
    // 创建快照文件
    QString snapshotPath = QString("snapshot_%1.dat").arg(version);
    QFile file(snapshotPath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        QDataStream out(&file);
        saveProject(out);
        file.close();
    }
}

void MeasureDataHandler::restoreSnapshot(int version)
{
    // 恢复快照文件
    QString snapshotPath = QString("snapshot_%1.dat").arg(version);
    QFile file(snapshotPath);
    if (file.open(QIODevice::ReadOnly)) {
        QDataStream in(&file);
        openProject(in);
        file.close();
    }
}

bool MeasureDataHandler::saveProject(QDataStream &out, qint32 version)
{
    stop();
    if (!out.device() || !out.device()->isWritable()) {
        qCritical() << "Save project failed: device not writable";
        return false;
    }

    // 增加边界检查
    if (tdcResolutionArray.size() != waveChannels) {
        qCritical() << "TDC resolution array size mismatch with wave channels";
        return false;
    }
    if (waveAxisArray.size() != waveChannels) {
        qCritical() << "Wave axis array size mismatch with wave channels";
        return false;
    }
    if (timeAxisArray.size() != timeChannels) {
        qCritical() << "Time axis array size mismatch with time channels";
        return false;
    }

    fileChangeTime = QDateTime::currentDateTime().toString("yyyy.MM.dd,hh:mm:ss");

    // 保存基本信息 (所有版本共有)
    out << errorFlag << ident << softwareVersion << hardwareVersion << fileTime << fileChangeTime << comment << measurementMode << displayLinLog << displayWaveAxisLower << displayWaveAxisUpper << displayTimeAxisLower << displayTimeAxisUpper << displayCountAxisLower << displayCountAxisUpper;
    if(out.status() != QDataStream::Ok) {
        qCritical() << "File header serialization failed";
        return false;
    }

    // 保存测量参数 (所有版本共有)
    out << monoValid << monoGrating << monoGroove << monoBlaze << monoWave << monoPort << monoShutter << monoFilter << tdcResolution << waveChannels << timeChannels;
    for (int i = 0; i < waveChannels; ++i) {
        if (i >= 0 && i < tdcResolutionArray.size()) {
            out << tdcResolutionArray[i];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "TDC resolution serialization failed at index" << i;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in tdcResolutionArray: " << i;
            return false;
        }
    }

    // 保存停止条件 (所有版本共有)
    out << stopCondition << maxMeasurementTiming << maxNumPhotons << startTime << endTime;
    if(out.status() != QDataStream::Ok) {
        qCritical() << "Measurement parameters serialization failed";
        return false;
    }

    // 保存波长和时间轴数据 (所有版本共有)
    for (int lambda = 0; lambda < waveChannels; ++lambda) {
        if (lambda >= 0 && lambda < waveAxisArray.size()) {
            out << waveAxisArray[lambda];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "Wave axis serialization failed at index" << lambda;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in waveAxisArray: " << lambda;
            return false;
        }
    }

    for (int t = 0; t < timeChannels; ++t) {
        if (t >= 0 && t < timeAxisArray.size()) {
            out << timeAxisArray[t];
            if(out.status() != QDataStream::Ok) {
                qCritical() << "Time axis serialization failed at index" << t;
                return false;
            }
        } else {
            qCritical() << "Index out of bounds in timeAxisArray: " << t;
            return false;
        }
    }

    dataArraySize += dataArray.size();

    // 序列化数据帧
    saveDataArrayToFile();

    // 保存数据数组 (所有版本共有)
    for (int lambda = 0; lambda < waveChannels; ++lambda) {
        if (lambda >= 0 && lambda < dataArrayTotal.size()) {
            for (int t = 0; t < timeChannels; ++t) {
                if (t >= 0 && t < dataArrayTotal[lambda].size()) {
                    out << dataArrayTotal[lambda][t];
                    if(out.status() != QDataStream::Ok) {
                        qCritical() << "Total data serialization failed at (" << lambda << "," << t << ")";
                        return false;
                    }
                } else {
                    qCritical() << "Index out of bounds in dataArrayTotal[lambda]: " << t;
                    return false;
                }
            }
        } else {
            qCritical() << "Index out of bounds in dataArrayTotal: " << lambda;
            return false;
        }
    }

    // 版本特定的数据保存
    if (version >= FileVersion::VERSION_1) {
        // 版本1及以上特有的数据
        out << dataArraySize;
        if(out.status() != QDataStream::Ok) {
            qCritical() << "Data array size serialization failed";
            return false;
        }
    }

    // 未来版本可以在这里添加更多的版本特定数据保存
    // if (version >= FileVersion::VERSION_2) {
    //     // 版本2及以上特有的数据
    //     out << newField1 << newField2;
    // }

    return true;
}

bool MeasureDataHandler::openProject(QDataStream &in, qint32 version)
{
    if (!in.device() || !in.device()->isReadable()) {
        qCritical() << "Open project failed: device not readable";
        return false;
    }

    // 读取基本信息 (所有版本共有)
    in >> errorFlag >> ident >> softwareVersion >> hardwareVersion >> fileTime >> fileChangeTime >> comment >> measurementMode >> displayLinLog >> displayWaveAxisLower >> displayWaveAxisUpper >> displayTimeAxisLower >> displayTimeAxisUpper >> displayCountAxisLower >> displayCountAxisUpper;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "File header deserialization failed";
        return false;
    }

    // 读取测量参数 (所有版本共有)
    in >> monoValid >> monoGrating >> monoGroove >> monoBlaze >> monoWave >> monoPort >> monoShutter >> monoFilter >> tdcResolution >> waveChannels >> timeChannels;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "Measurement parameters deserialization failed";
        return false;
    }

    // 增加边界检查
    if (waveChannels <= 0 || timeChannels <= 0) {
        qCritical() << "Invalid wave or time channels value";
        return false;
    }

    // 读取 TDC 分辨率数组 (所有版本共有)
    tdcResolutionArray.resize(waveChannels);
    for (int i = 0; i < waveChannels; ++i) {
        in >> tdcResolutionArray[i];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "TDC resolution deserialization failed at index" << i;
            return false;
        }
    }

    // 读取停止条件 (所有版本共有)
    in >> stopCondition >> maxMeasurementTiming >> maxNumPhotons >> startTime >> endTime;
    if(in.status() != QDataStream::Ok) {
        qCritical() << "Stop condition deserialization failed";
        return false;
    }

    // 读取波长和时间轴数据 (所有版本共有)
    waveAxisArray.resize(waveChannels);
    timeAxisArray.resize(timeChannels);

    for (int lambda = 0; lambda < waveChannels; ++lambda) {
        in >> waveAxisArray[lambda];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "Wave axis deserialization failed at index" << lambda;
            return false;
        }
    }
    for (int t = 0; t < timeChannels; ++t) {
        in >> timeAxisArray[t];
        if(in.status() != QDataStream::Ok) {
            qCritical() << "Time axis deserialization failed at index" << t;
            return false;
        }
    }

    // 读取数据数组 (所有版本共有)
    dataArrayTotal.resize(waveChannels);
    for (int lambda = 0; lambda < waveChannels; ++lambda) {
        dataArrayTotal[lambda].resize(timeChannels);
        for (int t = 0; t < timeChannels; ++t) {
            in >> dataArrayTotal[lambda][t];
            if(in.status() != QDataStream::Ok) {
                qCritical() << "Total data deserialization failed at (" << lambda << "," << t << ")";
                return false;
            }
        }
    }

    // 版本特定的数据读取
    if (version >= FileVersion::VERSION_1) {
        // 版本1及以上特有的数据
        in >> dataArraySize;
        if(in.status() != QDataStream::Ok) {
            qWarning() << "Data array size deserialization failed, using default value";
            dataArraySize = 0;  // 使用默认值
        }
    } else {
        // 旧版本没有 dataArraySize，设置为默认值
        dataArraySize = 0;
        qDebug() << "Legacy file format detected, dataArraySize set to 0";
    }

    // 未来版本可以在这里添加更多的版本特定数据读取
    // if (version >= FileVersion::VERSION_2) {
    //     // 版本2及以上特有的数据
    //     in >> newField1 >> newField2;
    // }

    return true;
}

bool MeasureDataHandler::saveProject() {
    QBuffer buffer;
    buffer.open(QIODevice::ReadWrite);
    QDataStream out(&buffer);

    // 保存当前版本的数据
    if (!saveProject(out, FileVersion::VERSION_CURRENT)) {
        return false;
    }

    QByteArray compressed = qCompress(buffer.data());

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        QMessageBox::critical(nullptr, "Error", "Cannot open file for writing: " + file.errorString());
        return false;
    }

    QDataStream fileOut(&file);
    fileOut.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    // 写入文件头
    fileOut << qint32(0x4D435A50);  // 魔数

    // 写入版本号 (确保是一个合理的值)
    fileOut << qint32(FileVersion::VERSION_CURRENT);  // 版本号

    // 写入压缩数据长度
    fileOut << qint32(compressed.size());  // 压缩数据长度

    // 写入压缩数据
    qint64 bytesWritten = fileOut.writeRawData(compressed.constData(), compressed.size());
    if (bytesWritten != compressed.size()) {
        qCritical() << "Failed to write all compressed data. Expected:" << compressed.size() << "Actual:" << bytesWritten;
        file.close();
        return false;
    }

    qDebug() << "Saved file with version:" << FileVersion::VERSION_CURRENT
             << "compressed size:" << compressed.size()
             << "total file size:" << file.size();

    file.flush();
    file.close();
    return true;
}

bool MeasureDataHandler::openProject(const QString &filePath) {
    try {
        this->filePath = filePath;
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            qCritical() << "Cannot open file for reading:" << file.errorString();
            // 不要在工作线程中显示 QMessageBox
            return false;
        }

        // 获取文件总大小，用于判断格式
        qint64 fileSize = file.size();

        QDataStream in(&file);
        in.setVersion(QDataStream::Qt_DefaultCompiledVersion);

        // 尝试读取魔数
        qint32 magic;
        in >> magic;

        if (magic == 0x4D435A50) {
            // 检查文件大小，判断是否为新格式
            // 新格式至少应该有: 魔数(4字节) + 版本号(4字节) + 压缩数据长度(4字节) + 一些压缩数据
            bool isNewFormat = (fileSize > 12);

            QByteArray compressed;
            qint32 version = FileVersion::VERSION_LEGACY;

            if (isNewFormat) {
                // 尝试作为新格式读取
                qint64 posAfterMagic = file.pos();

                // 读取版本号
                if (file.bytesAvailable() >= 4) {
                    in >> version;

                    // 检查版本号是否合理 (0-10)
                    if (version < 0 || version > 10) {
                        qWarning() << "Invalid version number:" << version << ", assuming legacy format";
                        version = FileVersion::VERSION_LEGACY;

                        // 回退到魔数后的位置，作为旧格式处理
                        file.seek(posAfterMagic);
                        in >> compressed;
                    } else {
                        qDebug() << "File version:" << version;

                        // 读取压缩数据长度
                        if (file.bytesAvailable() >= 4) {
                            qint32 compressedSize;
                            in >> compressedSize;

                            // 检查压缩数据长度是否合理
                            if (compressedSize > 0 && compressedSize <= file.bytesAvailable()) {
                                qDebug() << "Compressed data size:" << compressedSize;
                                compressed.resize(compressedSize);
                                in.readRawData(compressed.data(), compressedSize);
                            } else {
                                qWarning() << "Invalid compressed size:" << compressedSize << ", reading all remaining data";
                                compressed = file.readAll();
                            }
                        } else {
                            qWarning() << "Not enough data for compressed size, reading all remaining data";
                            compressed = file.readAll();
                        }
                    }
                } else {
                    qWarning() << "Not enough data for version, assuming legacy format";
                    // 回退到魔数后的位置，作为旧格式处理
                    file.seek(posAfterMagic);
                    in >> compressed;
                }
            } else {
                // 旧格式: 魔数后直接是压缩数据
                qDebug() << "Legacy format detected (small file size)";
                in >> compressed;
            }

            // 如果压缩数据为空，尝试读取所有剩余数据
            if (compressed.isEmpty()) {
                qWarning() << "Compressed data is empty, trying to read all remaining data";
                compressed = file.readAll();
            }

            // 尝试解压数据
            QByteArray uncompressed = qUncompress(compressed);

            // 如果解压失败，尝试直接读取整个文件并解压
            if (uncompressed.isEmpty()) {
                qWarning() << "Failed to uncompress data, trying alternative approach";
                file.seek(0);
                QByteArray allData = file.readAll();
                uncompressed = qUncompress(allData);

                if (uncompressed.isEmpty()) {
                    qCritical() << "All decompression attempts failed";
                    return false;
                }
            }

            // 通过缓冲区加载解压后的数据
            QBuffer buffer(&uncompressed);
            buffer.open(QIODevice::ReadOnly);
            QDataStream dataStream(&buffer);

            // 调用统一的读取方法，传入版本号
            bool result = openProject(dataStream, version);
            file.close();
            return result;
        } else {
            // 尝试作为旧格式文件处理 (没有魔数的情况)
            file.seek(0);
            QByteArray data = file.readAll();
            QByteArray uncompressed = qUncompress(data);

            if (uncompressed.isEmpty()) {
                qCritical() << "Invalid file format, magic number mismatch and direct decompression failed";
                return false;
            }

            // 通过缓冲区加载解压后的数据
            QBuffer buffer(&uncompressed);
            buffer.open(QIODevice::ReadOnly);
            QDataStream dataStream(&buffer);

            // 使用旧版本的读取方法
            bool result = openProject(dataStream, FileVersion::VERSION_LEGACY);
            file.close();
            return result;
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in openProject:" << e.what();
        // 不要在工作线程中显示 QMessageBox
        return false;
    } catch (...) {
        qCritical() << "Unknown exception in openProject";
        // 不要在工作线程中显示 QMessageBox
        return false;
    }
}

void MeasureDataHandler::createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const std::vector<std::vector<int>> &data)
{
    // 检查数据大小是否符合预期
    if (data.size() != waveChannels || data[0].size() != timeChannels) {
        qCritical() << "Invalid data size for DataFrame";
        return;
    }

    DataFrame newFrame;
    newFrame.frameNumber = frameNumber;
    newFrame.pulseCount = pulseCount;
    newFrame.data = data;
    dataArray.append(newFrame);
    dataArraySize += 1;

    // 更新上次保存时间
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

    // 检查系统内存使用情况
    qint64 availableMemory = getAvailableSystemMemory();

    // 如果可用内存低于1GB或dataArray超过frameInFile，保存数据
    if (availableMemory < 1024) {
        qDebug() << "Low memory detected (" << availableMemory << "MB). Forcing data save.";
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }
    // 如果dataArray超过frameInFile，保存到filePath+.n文件并清空dataArray
    else if (dataArray.size() >= frameInFile) {
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }
}

void MeasureDataHandler::saveDataArrayToFile()
{
    if (dataArray.isEmpty()) {
        return;
    }
    // 复制dataArray和其他必要的数据
    QList<DataFrame> dataArrayCopy = dataArray;
    QString filePathCopy = filePath;
    unsigned int waveChannelsCopy = waveChannels;
    unsigned int timeChannelsCopy = timeChannels;
    int fileCounterValue = fileCounter++;

    // 异步调用保存方法，但不要捕获 this 指针
    QtConcurrent::run([filePathCopy, dataArrayCopy, waveChannelsCopy, timeChannelsCopy, fileCounterValue]() {
        // 创建一个独立的保存函数，不依赖于类成员变量
        QString dataFilePath = QString("%1.%2").arg(filePathCopy).arg(fileCounterValue);
        QFile file(dataFilePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
            QBuffer buffer;
            buffer.open(QIODevice::ReadWrite);
            QDataStream out(&buffer);

            out << static_cast<qint32>(dataArrayCopy.size()); // 写入dataArrayCopy的大小
            for (const auto& frame : dataArrayCopy) {
                out << frame.frameNumber << frame.pulseCount;
                for (int lambda = 0; lambda < waveChannelsCopy; ++lambda) {
                    for (int t = 0; t < timeChannelsCopy; ++t) {
                        out << frame.data[lambda][t];
                    }
                }
            }

            QByteArray compressed = qCompress(buffer.data());

            QDataStream fileOut(&file);
            fileOut.setVersion(QDataStream::Qt_DefaultCompiledVersion);

            // 写入文件头
            fileOut << qint32(0x4D435A50); // 魔数标识压缩格式

            // 写入版本号 (确保是一个合理的值)
            fileOut << qint32(MeasureDataHandler::FileVersion::VERSION_CURRENT); // 版本号

            // 写入压缩数据长度
            fileOut << qint32(compressed.size()); // 压缩数据长度

            // 写入压缩数据
            qint64 bytesWritten = fileOut.writeRawData(compressed.constData(), compressed.size());
            if (bytesWritten != compressed.size()) {
                qCritical() << "Failed to write all compressed data. Expected:" << compressed.size() << "Actual:" << bytesWritten;
                file.close();
                return;
            }

            qDebug() << "Saved data file with version:" << MeasureDataHandler::FileVersion::VERSION_CURRENT
                     << "compressed size:" << compressed.size()
                     << "total file size:" << file.size();

            file.flush();
            file.close();
        } else {
            qCritical() << "Failed to open file for writing:" << dataFilePath;
        }
    });
}

void MeasureDataHandler::saveDataArrayToFileAsync(const QList<DataFrame> &dataArrayCopy)
{
    QString dataFilePath = QString("%1.%2").arg(filePath).arg(fileCounter++);
    QFile file(dataFilePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        QBuffer buffer;
        buffer.open(QIODevice::ReadWrite);
        QDataStream out(&buffer);

        out << static_cast<qint32>(dataArrayCopy.size()); // 写入dataArrayCopy的大小
        for (const auto& frame : dataArrayCopy) {
            out << frame.frameNumber << frame.pulseCount;
            for (int lambda = 0; lambda < waveChannels; ++lambda) {
                for (int t = 0; t < timeChannels; ++t) {
                    out << frame.data[lambda][t];
                }
            }
        }

        QByteArray compressed = qCompress(buffer.data());

        QDataStream fileOut(&file);
        fileOut.setVersion(QDataStream::Qt_DefaultCompiledVersion);

        // 写入文件头
        fileOut << qint32(0x4D435A50); // 魔数标识压缩格式

        // 写入版本号 (确保是一个合理的值)
        fileOut << qint32(FileVersion::VERSION_CURRENT); // 版本号

        // 写入压缩数据长度
        fileOut << qint32(compressed.size()); // 压缩数据长度

        // 写入压缩数据
        qint64 bytesWritten = fileOut.writeRawData(compressed.constData(), compressed.size());
        if (bytesWritten != compressed.size()) {
            qCritical() << "Failed to write all compressed data. Expected:" << compressed.size() << "Actual:" << bytesWritten;
            file.close();
            return;
        }

        qDebug() << "Saved data file with version:" << FileVersion::VERSION_CURRENT
                 << "compressed size:" << compressed.size()
                 << "total file size:" << file.size();

        file.flush();
        file.close();
    } else {
        qCritical() << "Failed to open file for writing:" << dataFilePath;
    }
}

void MeasureDataHandler::start(const QString &path)
{
    filePath = path;

    // 重置dataArray
    dataArray.clear();
    // 重置dataArraySize
    dataArraySize = 0;
    // 重置startTime
    startTime = QDateTime::currentMSecsSinceEpoch();
    // 重置endTime
    endTime = 0;
    // 重置saving
    saving = true;
    // 重置lastSaveTime
    m_lastSaveTime = startTime;

    // 重新计算最佳FrameInFile值
    calculateOptimalFrameInFile();
}

void MeasureDataHandler::stop()
{
    // 重置endTime
    endTime = QDateTime::currentMSecsSinceEpoch();
    // 重置saving
    saving = false;
}

// 实现新增方法：计算最佳FrameInFile值
void MeasureDataHandler::calculateOptimalFrameInFile()
{
    // 获取系统可用内存（MB）
    qint64 availableMemory = getAvailableSystemMemory();

    // 获取磁盘可用空间
    QStorageInfo storageInfo(QDir::currentPath());
    qint64 availableDiskSpace = storageInfo.bytesAvailable() / (1024 * 1024); // 转换为MB

    // 估算单个数据帧大小（MB）
    double frameSizeMB = static_cast<double>(m_estimatedFrameSize) / (1024 * 1024);

    // 计算最佳值：使用不超过系统可用内存的10%
    int optimalFrames = static_cast<int>(availableMemory * 0.1 / frameSizeMB);

    // 确保在合理范围内
    frameInFile = qBound(100, optimalFrames, 5000);

    qDebug() << "Calculated optimal FrameInFile:" << frameInFile
             << "(Available Memory:" << availableMemory << "MB, Frame Size:" << frameSizeMB << "MB)";
}

// 实现新增方法：检查并保存数据（基于时间的保存策略）
void MeasureDataHandler::checkAndSaveData()
{
    if (!saving || dataArray.isEmpty()) {
        return;
    }

    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    // 如果距离上次保存已经超过30秒，则保存数据
    if (currentTime - m_lastSaveTime > 30000) {
        qDebug() << "Time-based save triggered. Saving" << dataArray.size() << "frames.";
        // 使用安全的保存方法
        saveDataArrayToFile();
        dataArray.clear();
        m_lastSaveTime = currentTime;
    }

    // 检查系统内存使用情况
    qint64 availableMemory = getAvailableSystemMemory();
    // 如果可用内存低于1GB，强制保存
    if (availableMemory < 1024) {
        qDebug() << "Low memory detected (" << availableMemory << "MB). Forcing data save.";
        if (!dataArray.isEmpty()) {
            // 使用安全的保存方法
            saveDataArrayToFile();
            dataArray.clear();
            m_lastSaveTime = currentTime;
        }
    }
}

// 实现新增方法：获取系统可用内存（MB）
qint64 MeasureDataHandler::getAvailableSystemMemory()
{
#ifdef Q_OS_WIN
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    return memInfo.ullAvailPhys / (1024 * 1024); // 转换为MB
#else
    // 在非Windows系统上返回一个默认值
    return 4096; // 默认4GB
#endif
}

QList<MeasureDataHandler::DataFrame> MeasureDataHandler::getAllFramesData()
{
    QList<DataFrame> allFrames;

    // 首先添加内存中的当前帧数据
    allFrames.append(dataArray);

    // 然后读取所有分散的帧数据文件
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.dir();
    QString baseName = fileInfo.fileName();

    // 查找所有相关的数据文件 (.sfd.1, .sfd.2, 等)
    QStringList nameFilters;
    nameFilters << baseName + ".*";
    QStringList dataFiles = dir.entryList(nameFilters, QDir::Files, QDir::Name);

    // 过滤出数字后缀的文件并排序
    QRegularExpression re("\\.(\\d+)$");
    QMap<int, QString> sortedFiles;

    for (const QString& fileName : dataFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            bool ok;
            int fileNumber = match.captured(1).toInt(&ok);
            if (ok) {
                sortedFiles[fileNumber] = dir.filePath(fileName);
            }
        }
    }

    // 按顺序读取每个数据文件
    for (auto it = sortedFiles.begin(); it != sortedFiles.end(); ++it) {
        QList<DataFrame> framesFromFile = loadFramesFromDataFile(it.value());
        allFrames.append(framesFromFile);
    }

    qDebug() << "getAllFramesData: Loaded total" << allFrames.size() << "frames"
             << "(Memory:" << dataArray.size() << ", Files:" << sortedFiles.size() << ")";

    return allFrames;
}

QList<MeasureDataHandler::DataFrame> MeasureDataHandler::loadFramesFromDataFile(const QString& dataFilePath)
{
    QList<DataFrame> frames;

    QFile file(dataFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open data file:" << dataFilePath;
        return frames;
    }

    QDataStream in(&file);
    in.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    // 读取魔数和版本信息
    qint32 magic;
    in >> magic;

    QByteArray uncompressed;
    if (magic == 0x4D435A50) { // 有魔数的新格式
        qint32 version;
        in >> version;
        qint32 compressedSize;
        in >> compressedSize;

        QByteArray compressed(compressedSize, 0);
        in.readRawData(compressed.data(), compressedSize);
        uncompressed = qUncompress(compressed);
    } else {
        // 没有魔数的旧格式，整个文件都是压缩数据
        file.seek(0);
        QByteArray compressed = file.readAll();
        uncompressed = qUncompress(compressed);
    }

    if (uncompressed.isEmpty()) {
        qWarning() << "Failed to decompress data file:" << dataFilePath;
        file.close();
        return frames;
    }

    // 解析解压后的数据
    QBuffer buffer(&uncompressed);
    buffer.open(QIODevice::ReadOnly);
    QDataStream dataStream(&buffer);

    // 读取帧数量
    qint32 frameCount;
    dataStream >> frameCount;

    if (frameCount <= 0) {
        qWarning() << "Invalid frame count in data file:" << dataFilePath;
        file.close();
        return frames;
    }

    // 读取每一帧数据
    for (int i = 0; i < frameCount; ++i) {
        DataFrame frame;
        dataStream >> frame.frameNumber >> frame.pulseCount;

        // 读取帧数据
        frame.data.resize(waveChannels);
        for (int lambda = 0; lambda < waveChannels; ++lambda) {
            frame.data[lambda].resize(timeChannels);
            for (int t = 0; t < timeChannels; ++t) {
                dataStream >> frame.data[lambda][t];
            }
        }

        frames.append(frame);
    }

    file.close();
    qDebug() << "Loaded" << frameCount << "frames from data file:" << dataFilePath;

    return frames;
}
