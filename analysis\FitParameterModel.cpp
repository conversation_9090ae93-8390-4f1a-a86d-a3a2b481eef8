#include "FitParameterModel.h"
#include "FitModels.h"
#include <QDebug>

// 初始化静态实例
FitParameterModel* FitParameterModel::instance = nullptr;

FitParameterModel* FitParameterModel::getInstance() {
    if (!instance) {
        instance = new FitParameterModel();
    }
    return instance;
}

FitParameterModel::FitParameterModel(QObject* parent)
    : QObject(parent),
    m_analysisMethod("Fitting"),
    m_fittingModel("Tail Fitting"),
    m_t0(0.0),
    m_fwhm(0.0),
    m_analysisMode("Individual Analysis"),
    m_exponentialModel("N指数衰减"),
    m_modelParametersCount(1),
    m_modelAlgorithm("Least Square Method"),
    m_iterations(100000),  // 设置默认迭代次数为100000
    m_rangeFrom(47.0),

    m_rangeTo(90.0) {

    // 初始化参数列表
    updateParameterList();
}

FitParameterModel::~FitParameterModel() {
    // 清理资源
}

void FitParameterModel::setAnalysisMethod(const QString& method) {
    if (m_analysisMethod != method) {
        m_analysisMethod = method;
        emit analysisMethodChanged(method);
    }
}

void FitParameterModel::setFittingModel(const QString& model) {
    if (m_fittingModel != model) {
        m_fittingModel = model;
        emit fittingModelChanged(model);
    }
}

void FitParameterModel::setT0(double value) {
    if (!qFuzzyCompare(m_t0, value)) {
        m_t0 = value;
        emit t0Changed(value);
    }
}

void FitParameterModel::setFwhm(double value) {
    if (!qFuzzyCompare(m_fwhm, value)) {
        m_fwhm = value;
        emit fwhmChanged(value);
    }
}

void FitParameterModel::setAnalysisMode(const QString& mode) {
    if (m_analysisMode != mode) {
        m_analysisMode = mode;
        emit analysisModeChanged(mode);
    }
}

void FitParameterModel::setExponentialModel(const QString& model) {
    if (m_exponentialModel != model) {
        m_exponentialModel = model;
        emit exponentialModelChanged(model);

        // 指数模型变化时，需要更新参数列表
        updateParameterList();
    }
}

void FitParameterModel::setModelParametersCount(int count) {
    if (m_modelParametersCount != count) {
        m_modelParametersCount = count;
        emit modelParametersCountChanged(count);

        // 参数数量变化时，需要更新参数列表
        updateParameterList();
    }
}

void FitParameterModel::setModelAlgorithm(const QString& algorithm) {
    if (m_modelAlgorithm != algorithm) {
        m_modelAlgorithm = algorithm;
        emit modelAlgorithmChanged(algorithm);
    }
}

void FitParameterModel::setIterations(int count) {
    if (m_iterations != count) {
        m_iterations = count;
        emit iterationsChanged(count);
    }
}

void FitParameterModel::setRangeFrom(double value) {
    if (!qFuzzyCompare(m_rangeFrom, value)) {
        m_rangeFrom = value;
        emit rangeFromChanged(value);
    }
}

void FitParameterModel::setRangeTo(double value) {
    if (!qFuzzyCompare(m_rangeTo, value)) {
        m_rangeTo = value;
        emit rangeToChanged(value);
    }
}

void FitParameterModel::updateParameterList() {
    // 保存现有参数的值和约束
    QMap<QString, ParameterInfo> oldParams = m_parameters;

    // 清空参数列表
    m_parameters.clear();

    // 根据模型类型和参数数量创建参数列表
    if (m_exponentialModel == "N指数衰减" || m_exponentialModel == "Stretched Exponential") {
        // 指数衰减模型
        for (int i = 1; i <= m_modelParametersCount; ++i) {
            // 振幅参数
            QString ampName = QString("A%1").arg(i);
            ParameterInfo ampParam;
            ampParam.name = ampName;
            ampParam.displayName = QString("A%1 [kCnts]").arg(i);
            ampParam.value = oldParams.contains(ampName) ? oldParams[ampName].value : 100.0;
            ampParam.fixed = oldParams.contains(ampName) ? oldParams[ampName].fixed : false;
            ampParam.hasMinConstraint = oldParams.contains(ampName) ? oldParams[ampName].hasMinConstraint : false;
            ampParam.minValue = oldParams.contains(ampName) ? oldParams[ampName].minValue : 0.0;
            ampParam.hasMaxConstraint = oldParams.contains(ampName) ? oldParams[ampName].hasMaxConstraint : false;
            ampParam.maxValue = oldParams.contains(ampName) ? oldParams[ampName].maxValue : 1000.0;
            m_parameters[ampName] = ampParam;

            // 时间常数参数
            QString tauName = QString("τ%1").arg(i);
            ParameterInfo tauParam;
            tauParam.name = tauName;
            tauParam.displayName = QString("τ%1 [ns]").arg(i);
            tauParam.value = oldParams.contains(tauName) ? oldParams[tauName].value : 1.0;
            tauParam.fixed = oldParams.contains(tauName) ? oldParams[tauName].fixed : false;
            tauParam.hasMinConstraint = oldParams.contains(tauName) ? oldParams[tauName].hasMinConstraint : false;
            tauParam.minValue = oldParams.contains(tauName) ? oldParams[tauName].minValue : 0.01;
            tauParam.hasMaxConstraint = oldParams.contains(tauName) ? oldParams[tauName].hasMaxConstraint : false;
            tauParam.maxValue = oldParams.contains(tauName) ? oldParams[tauName].maxValue : 100.0;
            m_parameters[tauName] = tauParam;
        }

        // 如果是拉伸指数模型，添加β参数
        if (m_exponentialModel == "Stretched Exponential") {
            ParameterInfo betaParam;
            betaParam.name = "β";
            betaParam.displayName = "β";
            betaParam.value = oldParams.contains("β") ? oldParams["β"].value : 1.0;
            betaParam.fixed = oldParams.contains("β") ? oldParams["β"].fixed : false;
            betaParam.hasMinConstraint = oldParams.contains("β") ? oldParams["β"].hasMinConstraint : false;
            betaParam.minValue = oldParams.contains("β") ? oldParams["β"].minValue : 0.1;
            betaParam.hasMaxConstraint = oldParams.contains("β") ? oldParams["β"].hasMaxConstraint : false;
            betaParam.maxValue = oldParams.contains("β") ? oldParams["β"].maxValue : 2.0;
            m_parameters["β"] = betaParam;
        }

        // 背景参数
        ParameterInfo bParam;
        bParam.name = "B";
        bParam.displayName = "B [Cnts]";
        bParam.value = oldParams.contains("B") ? oldParams["B"].value : 0.0;
        bParam.fixed = oldParams.contains("B") ? oldParams["B"].fixed : false;
        bParam.hasMinConstraint = oldParams.contains("B") ? oldParams["B"].hasMinConstraint : false;
        bParam.minValue = oldParams.contains("B") ? oldParams["B"].minValue : 0.0;
        bParam.hasMaxConstraint = oldParams.contains("B") ? oldParams["B"].hasMaxConstraint : false;
        bParam.maxValue = oldParams.contains("B") ? oldParams["B"].maxValue : 1000.0;
        m_parameters["B"] = bParam;
    }

    // 通知参数变化
    emit parametersChanged();
}

QVector<FitParameterModel::ParameterInfo> FitParameterModel::getParameters() const {
    QVector<ParameterInfo> params;
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        params.append(it.value());
    }
    return params;
}

FitParameterModel::ParameterInfo FitParameterModel::getParameter(const QString& name) const {
    if (m_parameters.contains(name)) {
        return m_parameters[name];
    }
    return ParameterInfo();
}

QStringList FitParameterModel::getParameterNames() const {
    return m_parameters.keys();
}

void FitParameterModel::setParameterValue(const QString& name, double value) {
    if (m_parameters.contains(name) && !qFuzzyCompare(m_parameters[name].value, value)) {
        m_parameters[name].value = value;
        emit parameterChanged(name);
        emit parametersChanged();
    }
}

void FitParameterModel::setParameterFixed(const QString& name, bool fixed) {
    if (m_parameters.contains(name) && m_parameters[name].fixed != fixed) {
        m_parameters[name].fixed = fixed;
        emit parameterChanged(name);
        emit parametersChanged();
    }
}

void FitParameterModel::setParameterRange(const QString& name, bool hasMin, double min, bool hasMax, double max) {
    if (m_parameters.contains(name)) {
        bool changed = false;

        if (m_parameters[name].hasMinConstraint != hasMin) {
            m_parameters[name].hasMinConstraint = hasMin;
            changed = true;
        }

        if (hasMin && !qFuzzyCompare(m_parameters[name].minValue, min)) {
            m_parameters[name].minValue = min;
            changed = true;
        }

        if (m_parameters[name].hasMaxConstraint != hasMax) {
            m_parameters[name].hasMaxConstraint = hasMax;
            changed = true;
        }

        if (hasMax && !qFuzzyCompare(m_parameters[name].maxValue, max)) {
            m_parameters[name].maxValue = max;
            changed = true;
        }

        if (changed) {
            emit parameterChanged(name);
            emit parametersChanged();
        }
    }
}

void FitParameterModel::resetToDefaults() {
    // 重置所有属性到默认值
    setAnalysisMethod("Fitting");
    setFittingModel("Tail Fitting");
    setT0(0.0);
    setFwhm(0.0);
    setAnalysisMode("Individual Analysis");
    setExponentialModel("Exponential");
    setModelParametersCount(1);
    setModelAlgorithm("Least Square Method");
    setIterations(100000);  // 重置为默认迭代次数100000
    setRangeFrom(0.0);
    setRangeTo(10.0);

    // 重置所有参数到默认值
    updateParameterList();
}

// IRF数据相关方法
void FitParameterModel::setIRFData(const QVector<double>& irfData) {
    if (m_irfData != irfData) {
        m_irfData = irfData;
        emit irfDataChanged();
    }
}

QVector<double> FitParameterModel::getIRFData() const {
    return m_irfData;
}

void FitParameterModel::clearIRFData() {
    if (!m_irfData.isEmpty()) {
        m_irfData.clear();
        emit irfDataChanged();
    }
}

QSharedPointer<FitModelInterface> FitParameterModel::createFitModel() {
    QSharedPointer<FitModelInterface> model;

    // 根据模型类型和参数数量创建不同的拟合模型
    if (m_exponentialModel == "N指数衰减") {
        QSharedPointer<NExponentialDecayModel> nExpModel = QSharedPointer<NExponentialDecayModel>(new NExponentialDecayModel(m_modelParametersCount));
        if (m_modelParametersCount < 1) {
            qDebug() << "Invalid model parameters count:" << m_modelParametersCount;
            return nullptr;
        }

        // 设置拟合算法
        if (m_modelAlgorithm == "Least Square Method") {
            nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
            qDebug() << "Using Least Square Method";
        } else if (m_modelAlgorithm == "Maximum Likelihood Estimation") {
            nExpModel->setFitAlgorithm(NExponentialDecayModel::MaximumLikelihood);
            qDebug() << "Using Maximum Likelihood Estimation";
        } else if (m_modelAlgorithm == "Bayesian Analysis") {
            nExpModel->setFitAlgorithm(NExponentialDecayModel::BayesianAnalysis);
            qDebug() << "Using Bayesian Analysis";
        } else {
            // 默认使用最小二乘法
            nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
            qDebug() << "Unknown algorithm:" << m_modelAlgorithm << ", using default Least Square Method";
        }

        // 如果有IRF数据，设置到模型中用于卷积拟合
        if (!m_irfData.isEmpty()) {
            nExpModel->setIRFData(m_irfData);
            qDebug() << "Setting IRF data for convolution fitting with" << m_irfData.size() << "points";
        }

        model = nExpModel;
    } else if (m_exponentialModel == "Stretched Exponential") {
        // 可以扩展实现拉伸指数模型
        qDebug() << "Stretched Exponential model not implemented yet";
        return nullptr;
    } else {
        qDebug() << "Unknown exponential model:" << m_exponentialModel;
        return nullptr;
    }

    // 设置模型参数
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        const QString& name = it.key();
        const ParameterInfo& info = it.value();

        // 设置参数值
        model->setParameter(name, info.value);

        // 设置参数是否固定
        model->setParameterFixed(name, info.fixed);

        // 设置参数范围
        if (info.hasMinConstraint && info.hasMaxConstraint) {
            model->setParameterRange(name, info.minValue, info.maxValue);
        }
    }

    return model;
}
