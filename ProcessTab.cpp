#include "ProcessTab.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QStackedWidget>
#include <QGroupBox>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QFrame>
#include <QFileDialog>
#include <QFileSystemModel>
#include <QTreeView>
#include <QSplitter>
#include <QLineEdit>
#include <QMessageBox>
#include <QMainWindow>
#include <QStatusBar>
#include "OpenProjectWidget.h" // 添加头文件
#include "CustomizePlot.h"
#include "SharedDataManager.h" // 添加 SharedDataManager 头文件
#include "ProcessAnalysisDataHandler.h" // 添加 ProcessAnalysisDataHandler 头文件
#include "ProjectFileManager.h" // 添加 ProjectFileManager 头文件

#include <QToolButton>
#include <QIcon>
#include <QTimer>
#include <cmath>

QTreeWidget* ProcessTab::createProcessTreeWidget() {
    QTreeWidget* treeWidget = new QTreeWidget();
    treeWidget->setHeaderHidden(true); // 隐藏表头

    // 设置样式表以隐藏节点连接线
    treeWidget->setStyleSheet("QTreeView::branch { border-image: none; }");

    // 设置所有项的缩进为0，使其靠左对齐
    treeWidget->setIndentation(20);

    // Alignment Section
    QStringList alignmentItems;
    alignmentItems << QString("Alignment");
    QTreeWidgetItem* alignmentParent = new QTreeWidgetItem(alignmentItems);

    // 创建子节点
    QTreeWidgetItem* autoAlignChild = new QTreeWidgetItem();
    QTreeWidgetItem* manualAlignChild = new QTreeWidgetItem();

    alignmentParent->addChild(autoAlignChild);
    alignmentParent->addChild(manualAlignChild);

    // 创建自动对齐的子节点内容
    QWidget* autoAlignWidget = new QWidget();
    QVBoxLayout* autoAlignLayout = new QVBoxLayout(autoAlignWidget);

    autoAlignButton = new QPushButton("Automatic");
    autoAlignValue = new QDoubleSpinBox(); // 使用 QSpinBox 替换 QLineEdit
    autoAlignValue->setRange(0, 9999); // 设置合适的范围
    autoAlignValue->setValue(0); // 设置默认值
    QLabel* autoAlignUnit = new QLabel("ns"); // 添加单位标签
    // Always enable the input field
    autoAlignValue->setEnabled(true);

    QHBoxLayout* alignmentRow1 = new QHBoxLayout();
    alignmentRow1->addWidget(autoAlignButton);
    alignmentRow1->addWidget(autoAlignValue);
    alignmentRow1->addWidget(autoAlignUnit); // 添加单位标签到布局
    alignmentRow1->addStretch();

    autoAlignLayout->addLayout(alignmentRow1);

    // 创建手动对齐的子节点内容
    QWidget* manualAlignWidget = new QWidget();
    QVBoxLayout* manualAlignLayout = new QVBoxLayout(manualAlignWidget);

    QHBoxLayout* alignmentRow2 = new QHBoxLayout();
    manualAlignButton = new QPushButton("Manual");
    // 添加四方形箭头调解控件到手动对齐子节点
    crossControl = new CrossControl();
    crossControl->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed); // 确保控件不被拉伸
    alignmentRow2->addWidget(manualAlignButton);
    alignmentRow2->addWidget(crossControl);
    alignmentRow2->addStretch();

    manualAlignLayout->addLayout(alignmentRow2);

    // 将子节点内容设置到树状控件中
    treeWidget->setItemWidget(autoAlignChild, 0, autoAlignWidget);
    treeWidget->setItemWidget(manualAlignChild, 0, manualAlignWidget);

    // 将父节点插入到树状控件中
    treeWidget->insertTopLevelItem(treeWidget->topLevelItemCount(), alignmentParent);

    // Crop Section
    QStringList cropItems;
    cropItems << QString("Crop");
    QTreeWidgetItem* cropParent = new QTreeWidgetItem(cropItems);

    // 创建子节点
    QTreeWidgetItem* hRangeChild = new QTreeWidgetItem();
    QTreeWidgetItem* vRangeChild = new QTreeWidgetItem();

    cropParent->addChild(hRangeChild);
    cropParent->addChild(vRangeChild);

    // ... existing code ...
// ... existing code ...
// 创建 V Range 子节点内容
QWidget* vRangeWidget = new QWidget();
QVBoxLayout* vRangeLayout = new QVBoxLayout(vRangeWidget);

QLabel* vRangeFromLabel = new QLabel("λ: From:");
vRangeFrom = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
vRangeFrom->setRange(0, 9999); // 设置合适的范围
vRangeFrom->setValue(530); // 设置默认值
// 设置最小宽度
vRangeFrom->setMinimumWidth(80);
QLabel* vRangeToLabel = new QLabel("to:");
vRangeTo = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
vRangeTo->setRange(0, 9999); // 设置合适的范围
vRangeTo->setValue(670); // 设置默认值
// 设置最小宽度
vRangeTo->setMinimumWidth(80);

QHBoxLayout* vRangeRow = new QHBoxLayout();
vRangeRow->addWidget(vRangeFromLabel);
vRangeRow->addWidget(vRangeFrom);
vRangeRow->addWidget(vRangeToLabel);
vRangeRow->addWidget(vRangeTo);
vRangeLayout->addLayout(vRangeRow);

// 创建 H Range 子节点内容
QWidget* hRangeWidget = new QWidget();
QVBoxLayout* hRangeLayout = new QVBoxLayout(hRangeWidget);

QLabel* hRangeFromLabel = new QLabel("T: From:");
hRangeFrom = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
hRangeFrom->setRange(0, 9999); // 设置合适的范围
hRangeFrom->setValue(20); // 设置默认值
// 设置最小宽度
hRangeFrom->setMinimumWidth(80);
QLabel* hRangeToLabel = new QLabel("to:");
hRangeTo = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
hRangeTo->setRange(0, 9999); // 设置合适的范围
hRangeTo->setValue(80); // 设置默认值
// 设置最小宽度
hRangeTo->setMinimumWidth(80);

QHBoxLayout* hRangeRow = new QHBoxLayout();
hRangeRow->addWidget(hRangeFromLabel);
hRangeRow->addWidget(hRangeFrom);
hRangeRow->addWidget(hRangeToLabel);
hRangeRow->addWidget(hRangeTo);
hRangeLayout->addLayout(hRangeRow);

// 创建一个新的水平布局来包含 H Range、V Range 和 Apply 按钮
QWidget* cropWidget = new QWidget();
QHBoxLayout* cropLayout = new QHBoxLayout(cropWidget);

// 创建一个垂直布局来包含 H Range 和 V Range
QVBoxLayout* leftCropLayout = new QVBoxLayout();
// 添加间距，让两行控件不那么挤
leftCropLayout->setSpacing(10);
leftCropLayout->addWidget(vRangeWidget);
leftCropLayout->addWidget(hRangeWidget);

// 将左侧布局添加到水平布局
cropLayout->addLayout(leftCropLayout);

applyCropButton = new QPushButton("Apply");
// 设置按钮的最小宽度
applyCropButton->setMinimumWidth(70);

// 创建一个水平弹簧，让按钮右对齐
QSpacerItem* horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);
cropLayout->addItem(horizontalSpacer);
cropLayout->addWidget(applyCropButton);

// 将子节点内容设置到树状控件中
treeWidget->setItemWidget(hRangeChild, 0, cropWidget);
treeWidget->setItemWidget(vRangeChild, 0, nullptr); // 清除原来的设置

    // 将父节点插入到树状控件中
    treeWidget->insertTopLevelItem(treeWidget->topLevelItemCount(), cropParent);

    // CurveAdd Section
    QStringList curveAddItems;
    curveAddItems << QString("Curve");
    QTreeWidgetItem* curveAddParent = new QTreeWidgetItem(curveAddItems);

    // 创建子节点
    QTreeWidgetItem* decayCurveChild = new QTreeWidgetItem();
    QTreeWidgetItem* spectralCurveChild = new QTreeWidgetItem();

    curveAddParent->addChild(decayCurveChild);
    curveAddParent->addChild(spectralCurveChild);

// ... existing code ...
    // 创建 Decay Curve 子节点内容
    QWidget* decayCurveWidget = new QWidget();
    QVBoxLayout* decayCurveLayout = new QVBoxLayout(decayCurveWidget);

    // QLabel* decayCurveLabel = new QLabel("Decay"); 已删除
    QLabel* decayCurveλLabel = new QLabel("Decay:     λ:");

    decayCurveLambda = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
    decayCurveLambda->setRange(0, 9999); // 设置合适的范围
    decayCurveLambda->setValue(20); // 设置默认值
    QLabel* decayCurvePlusMinus = new QLabel("±");
    decayCurveDelta = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
    decayCurveDelta->setRange(-9999, 9999); // 设置合适的范围
    decayCurveDelta->setValue(0); // 设置默认值
    decayCurveAddButton = new QPushButton("Add");
    decayCurveAddButton->setStyleSheet("min-width: 30px;");

    // QHBoxLayout* decayCurveRowSubTitle = new QHBoxLayout();
    // decayCurveRowSubTitle->addWidget(decayCurveLabel); 已删除

    QHBoxLayout* decayCurveRow = new QHBoxLayout();
    decayCurveRow->addWidget(decayCurveλLabel);

    decayCurveRow->addWidget(decayCurveLambda);
    decayCurveRow->addWidget(decayCurvePlusMinus);
    decayCurveRow->addWidget(decayCurveDelta);
    decayCurveRow->addWidget(decayCurveAddButton);

    // decayCurveLayout->addLayout(decayCurveRowSubTitle); 已删除
    decayCurveLayout->addLayout(decayCurveRow);

    // 创建 Spectral Curve 子节点内容
    QWidget* spectralCurveWidget = new QWidget();
    QVBoxLayout* spectralCurveLayout = new QVBoxLayout(spectralCurveWidget);

    // QLabel* spectralCurveLabel = new QLabel("Spectral"); 已删除
    QLabel* spectralCurveτLabel = new QLabel("Spectral:  T:");
    spectralCurveTau = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
    spectralCurveTau->setRange(0, 9999); // 设置合适的范围
    spectralCurveTau->setValue(340); // 设置默认值
    QLabel* spectralCurvePlusMinus = new QLabel("±");
    spectralCurveDelta = new QSpinBox(); // 使用 QSpinBox 替换 QLineEdit
    spectralCurveDelta->setRange(-9999, 9999); // 设置合适的范围
    spectralCurveDelta->setValue(0); // 设置默认值
    spectralCurveAddButton = new QPushButton("Add");
    spectralCurveAddButton->setStyleSheet("min-width: 30px;");

    // QHBoxLayout* spectralCurveRowSubTitle = new QHBoxLayout();
    // spectralCurveRowSubTitle->addWidget(spectralCurveLabel); 已删除

    QHBoxLayout* spectralCurveRow = new QHBoxLayout();
    spectralCurveRow->addWidget(spectralCurveτLabel);
    spectralCurveRow->addWidget(spectralCurveTau);
    spectralCurveRow->addWidget(spectralCurvePlusMinus);
    spectralCurveRow->addWidget(spectralCurveDelta);
    spectralCurveRow->addWidget(spectralCurveAddButton);

    // spectralCurveLayout->addLayout(spectralCurveRowSubTitle); 已删除
    spectralCurveLayout->addLayout(spectralCurveRow);
// ... existing code ...

// 将子节点内容设置到树状控件中
treeWidget->setItemWidget(decayCurveChild, 0, decayCurveWidget);
treeWidget->setItemWidget(spectralCurveChild, 0, spectralCurveWidget);

// 将父节点插入到树状控件中
treeWidget->insertTopLevelItem(treeWidget->topLevelItemCount(), curveAddParent);

// 创建 Split 节点
QStringList items_split;
items_split << QString("Split");
QTreeWidgetItem* splitParent = new QTreeWidgetItem(items_split);
treeWidget->insertTopLevelItem(treeWidget->topLevelItemCount(), splitParent);

// 创建 QStackedWidget 作为 Split 的子节点
QWidget* splitWidget = new QWidget();
QVBoxLayout* splitLayout = new QVBoxLayout(splitWidget);

// 创建 All、Single、Range 按钮
allButton = new QPushButton("All");
// 设置 All 按钮的默认背景颜色
allButton->setStyleSheet("background-color: #FF6666;");
singleButton = new QPushButton("Single");
rangeButton = new QPushButton("Range");

// 定义一个槽函数来处理按钮点击事件
auto handleButtonClick = [this](QPushButton* clickedButton) {
    allButton->setStyleSheet("");
    singleButton->setStyleSheet("");
    rangeButton->setStyleSheet("");
    clickedButton->setStyleSheet("background-color: #FF6666;");
};
// 连接按钮的 clicked 信号到槽函数
connect(allButton, &QPushButton::clicked, [this, handleButtonClick]() {
    handleButtonClick(allButton);
});
connect(singleButton, &QPushButton::clicked, [this, handleButtonClick]() {
    handleButtonClick(singleButton);
});
connect(rangeButton, &QPushButton::clicked, [this, handleButtonClick]() {
    handleButtonClick(rangeButton);
});

QHBoxLayout* buttonLayout = new QHBoxLayout();
buttonLayout->addWidget(allButton);
buttonLayout->addWidget(singleButton);
buttonLayout->addWidget(rangeButton);

splitLayout->addLayout(buttonLayout);

// 创建 QStackedWidget
QStackedWidget* stackedWidget = new QStackedWidget();

// 创建 All 页面
QWidget* allPage = new QWidget();
stackedWidget->addWidget(allPage);

// 创建 Single 页面
QWidget* singlePage = new QWidget();
// 使用垂直布局来管理两行内容
QVBoxLayout* mainSingleLayout = new QVBoxLayout(singlePage);

// 第一行布局
QHBoxLayout* firstRowLayout = new QHBoxLayout();
// 添加 t 标签
QLabel* tLabel = new QLabel("t");
firstRowLayout->addWidget(tLabel);

// 添加可拖动的播放条
QSlider* playSlider = new QSlider(Qt::Horizontal);
playSlider->setRange(0, 10000); // 对应 0.00 到 1000.00，精度到小数点后两位
playSlider->setValue(0);
firstRowLayout->addWidget(playSlider);

// 添加输入框
QDoubleSpinBox* inputSpinBox = new QDoubleSpinBox();
inputSpinBox->setDecimals(2);
inputSpinBox->setRange(0.00, 1000.00);
inputSpinBox->setValue(0.00);
inputSpinBox->setMinimumWidth(80);
firstRowLayout->addWidget(inputSpinBox);

// 添加 s 标签
QLabel* sLabel = new QLabel("s");
firstRowLayout->addWidget(sLabel);

// 连接播放条和输入框的信号槽，使它们的值同步
QObject::connect(playSlider, &QSlider::valueChanged, [inputSpinBox](int value) {
    inputSpinBox->setValue(value / 100.0);
});
QObject::connect(inputSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), [playSlider](double value) {
    playSlider->setValue(static_cast<int>(value * 100));
});

mainSingleLayout->addLayout(firstRowLayout);

// 第二行布局
QHBoxLayout* secondRowLayout = new QHBoxLayout();

// 设置按钮的大小策略和固定尺寸
QSizePolicy buttonSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
// 缩小宽度到原来的三分之一
int buttonWidth = 80 / 3;
// 缩小高度到原来的三分之一
int buttonHeight = 60 / 3;

// 添加播放器文字按钮
QPushButton* speedButton = new QPushButton("×24");
speedButton->setSizePolicy(buttonSizePolicy);
speedButton->setFixedSize(buttonWidth, buttonHeight);
QPushButton* back4sButton = new QPushButton("|◀");
back4sButton->setSizePolicy(buttonSizePolicy);
back4sButton->setFixedSize(buttonWidth, buttonHeight);
QPushButton* back2sButton = new QPushButton("|◀◀");
back2sButton->setSizePolicy(buttonSizePolicy);
back2sButton->setFixedSize(buttonWidth, buttonHeight);
playPauseButton = new QPushButton("▶");
playPauseButton->setSizePolicy(buttonSizePolicy);
playPauseButton->setFixedSize(buttonWidth, buttonHeight);
QPushButton* forward2sButton = new QPushButton("▶|");
forward2sButton->setSizePolicy(buttonSizePolicy);
forward2sButton->setFixedSize(buttonWidth, buttonHeight);
QPushButton* forward4sButton = new QPushButton("▶▶|");
forward4sButton->setSizePolicy(buttonSizePolicy);
forward4sButton->setFixedSize(buttonWidth, buttonHeight);
QPushButton* loopButton = new QPushButton("⟳");
loopButton->setSizePolicy(buttonSizePolicy);
loopButton->setFixedSize(buttonWidth, buttonHeight);

secondRowLayout->addWidget(speedButton);
secondRowLayout->addWidget(back4sButton);
secondRowLayout->addWidget(back2sButton);
secondRowLayout->addWidget(playPauseButton);
secondRowLayout->addWidget(forward2sButton);
secondRowLayout->addWidget(forward4sButton);
secondRowLayout->addWidget(loopButton);

// 添加弹簧使 Export 按钮右对齐
secondRowLayout->addStretch();

// 添加 Export 按钮
QPushButton* exportButton = new QPushButton("Export");
exportButton->setMinimumWidth(70);
secondRowLayout->addWidget(exportButton);

mainSingleLayout->addLayout(secondRowLayout);

stackedWidget->addWidget(singlePage);

// 创建 Range 页面
QWidget* rangePage = new QWidget();
QHBoxLayout* rangeLayout = new QHBoxLayout(rangePage);
// 设置布局左对齐
rangeLayout->setAlignment(Qt::AlignLeft);
// 设置布局的间距更小，让控件更紧凑
rangeLayout->setSpacing(5);

QLabel* rangeFromLabel = new QLabel("     From:");
// 使用 QDoubleSpinBox 替换 QSpinBox
QDoubleSpinBox* rangeFromSpinBox = new QDoubleSpinBox();
// 设置小数位数为 2
rangeFromSpinBox->setDecimals(2);
// 设置最小值为 0.00
rangeFromSpinBox->setMinimum(0.00);
// 设置最大值为 1000.00
rangeFromSpinBox->setMaximum(1000.00);
// 设置默认值为 0.00
rangeFromSpinBox->setValue(0.00);
// 设置固定宽度为 80
rangeFromSpinBox->setFixedWidth(80);

QLabel* rangeToLabel = new QLabel("to:");
// 使用 QDoubleSpinBox 替换 QSpinBox
QDoubleSpinBox* rangeToSpinBox = new QDoubleSpinBox();
// 设置小数位数为 2
rangeToSpinBox->setDecimals(2);
// 设置最小值为 0.00
rangeToSpinBox->setMinimum(0.00);
// 设置最大值为 1000.00
rangeToSpinBox->setMaximum(1000.00);
// 设置默认值为 111.11
rangeToSpinBox->setValue(111.11);
// 设置固定宽度为 80
rangeToSpinBox->setFixedWidth(80);

QLabel* rangeSLabel = new QLabel("s");

rangeLayout->addWidget(rangeFromLabel);
rangeLayout->addWidget(rangeFromSpinBox);
rangeLayout->addWidget(rangeToLabel);
rangeLayout->addWidget(rangeToSpinBox);
rangeLayout->addWidget(rangeSLabel);

stackedWidget->addWidget(rangePage);

// 将 QStackedWidget 添加到 Split 布局中
splitLayout->addWidget(stackedWidget);

// 连接按钮信号到 QStackedWidget 的 setCurrentIndex
connect(allButton, &QPushButton::clicked, this, [this, stackedWidget, handleButtonClick]() {
    handleButtonClick(allButton);
    stackedWidget->setCurrentIndex(0); // 切换到 All 页面
});
connect(singleButton, &QPushButton::clicked, this, [this, stackedWidget, handleButtonClick]() {
    handleButtonClick(singleButton);
    stackedWidget->setCurrentIndex(1); // 切换到 Single 页面
});
connect(rangeButton, &QPushButton::clicked, this, [this, stackedWidget, handleButtonClick]() {
    handleButtonClick(rangeButton);
    stackedWidget->setCurrentIndex(2); // 切换到 Range 页面
});

// 设置默认显示 All 页面
stackedWidget->setCurrentIndex(0);

// 创建 Split 的子节点
QTreeWidgetItem* splitChild = new QTreeWidgetItem();
splitParent->addChild(splitChild);

// 将 Split Widget 添加到树节点
treeWidget->setItemWidget(splitChild, 0, splitWidget);
    // 默认不展开所有顶级项
    treeWidget->expandAll();
    return treeWidget;
}
// 构造函数实现
ProcessTab::ProcessTab(QWidget* parent)
    : BaseTab(parent, TabType::ProcessTab),
    currentAlignment(0),
    manualAlignmentSelected(false),
    manualAlignButton(nullptr),
    autoAlignButton(nullptr),
    applyCropButton(nullptr),
    decayCurveAddButton(nullptr),
    spectralCurveAddButton(nullptr),
    autoAlignValue(nullptr),
    resetButton(nullptr),
    playPauseButton(nullptr),
    playTimer(nullptr),
    isPlaying(false),
    currentFrameIndex(0)
{

    // 初始化 currentAlignment
    // 创建主布局，设置为左右分栏
    QHBoxLayout* mainLayout1 = new QHBoxLayout(this);
    mainLayout1->setContentsMargins(0, 0, 0, 0); // 设置布局的边距为0，与Analysis画面保持一致
    mainLayout1->setSpacing(0);                  // 设置布局元素之间的间距为0，与Analysis画面保持一致

    setupPlots();
    // 左侧布局（文件目录和功能按钮切换）
    QVBoxLayout* leftLayout = new QVBoxLayout();
    leftLayout->setContentsMargins(0, 0, 0, 0); // 设置左侧布局的边距为0，与Analysis画面保持一致
    leftLayout->setSpacing(0); // 设置左侧布局的间距为0，与Analysis画面保持一致

    // 顶部按钮区域（文件目录和 process 页面切换）
    QHBoxLayout* topButtonLayout = new QHBoxLayout();
    topButtonLayout->setContentsMargins(0, 0, 0, 0); // 设置顶部按钮布局的边距为0
    topButtonLayout->setSpacing(2); // 设置顶部按钮布局的间距为2像素

    QPushButton* openFileButton = new QPushButton("WorkSpace");
    QPushButton* processButton = new QPushButton("Process");

    // 设置按钮的大小策略
    openFileButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    processButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 不设置固定宽度，使用QSS样式控制

    // 设置按钮的样式类
    openFileButton->setProperty("class", "StandardButton");
    processButton->setProperty("class", "StandardButton");

    // 设置按钮为可选中状态
    openFileButton->setCheckable(true);
    processButton->setCheckable(true);

    topButtonLayout->addWidget(openFileButton);
    topButtonLayout->addWidget(processButton);

    // 不添加拉伸因子，确保按钮占满整个布局

    leftLayout->addLayout(topButtonLayout);

    // 创建文件目录和 process 页面切换容器
    QStackedWidget* stackedWidget = new QStackedWidget();
    leftLayout->addWidget(stackedWidget, 1);

    // 页面1：文件目录页面
    // 创建 OpenProjectWidget 实例
    OpenProjectWidget* openProjectWidget = new OpenProjectWidget(this);

    // 将 OpenProjectWidget 添加到布局中
    stackedWidget->addWidget(openProjectWidget);

    // 连接信号和槽
    connect(openProjectWidget, &OpenProjectWidget::dataRowClicked, this, &ProcessTab::onFileSelected);

    // 页面2：process 功能按钮页面
    QWidget* processPage = new QWidget();
    QVBoxLayout* processPageLayout = new QVBoxLayout(processPage);

    // 使用 QTreeWidget 显示可折叠功能按钮
    processTreeWidget = createProcessTreeWidget();
    processTreeWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 设置主布局的拉伸因子
    QVBoxLayout* mainProcessLayout = new QVBoxLayout();
    mainProcessLayout->setContentsMargins(0, 0, 0, 0);
    mainProcessLayout->setSpacing(0);

    // processTreeWidget 占据所有可用空间
    processTreeWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    mainProcessLayout->addWidget(processTreeWidget, 1);

    // Undo/Reset/Save/Help Section
    QWidget* bottomWidget = new QWidget();
    QHBoxLayout* bottomLayout = new QHBoxLayout(bottomWidget);
    bottomLayout->setContentsMargins(0, 0, 0, 0);

    undoButton = new QPushButton("Undo");
    resetButton = new QPushButton("Reset");
    saveButton = new QPushButton("Save");
    helpButton = new QPushButton("Help");

    bottomLayout->addWidget(undoButton);
    bottomLayout->addWidget(resetButton);
    bottomLayout->addWidget(saveButton);
    bottomLayout->addWidget(helpButton);

    // 底部工具栏固定在底部
    mainProcessLayout->addWidget(bottomWidget, 0, Qt::AlignBottom);

    // 将主布局添加到页面布局
    processPageLayout->addLayout(mainProcessLayout);
    stackedWidget->addWidget(processPage);

    // 将左侧布局添加到主布局
    mainLayout1->addLayout(leftLayout, 1); // 左侧占比1

    // 右侧布局（图像显示部分）
    QGridLayout* rightLayout = new QGridLayout();
    rightLayout->setContentsMargins(0, 0, 0, 0); // 设置右侧布局的边距为0，与Analysis画面保持一致
    rightLayout->setSpacing(0); // 设置右侧布局的间距为0，与Analysis画面保持一致


    // 使用基类的鼠标事件处理函数
    // 基类的setupPlots已经连接了基本的鼠标事件

    // Create a container for m_pPlot1 and its toolbar
    QWidget* plot1Container = new QWidget();
    QHBoxLayout* plot1Layout = new QHBoxLayout(plot1Container);
    plot1Layout->setContentsMargins(0, 0, 0, 0);
    plot1Layout->setSpacing(0);
    plot1Layout->addWidget(m_pPlot1);

    // Create a container for m_pPlot2 and its toolbar
    QWidget* plot2Container = new QWidget();
    QHBoxLayout* plot2Layout = new QHBoxLayout(plot2Container);
    plot2Layout->setContentsMargins(0, 0, 0, 0);
    plot2Layout->setSpacing(0);
    plot2Layout->addWidget(m_pPlot2);

    // Create a container for m_pPlot3 and its toolbar
    QWidget* plot3Container = new QWidget();
    QHBoxLayout* plot3Layout = new QHBoxLayout(plot3Container);
    plot3Layout->setContentsMargins(0, 0, 0, 0);
    plot3Layout->setSpacing(0);
    plot3Layout->addWidget(m_pPlot3);

    // Setup plot toolbars
    setupPlotToolbars();
    // 将工具栏添加到布局中，使其与 plot 左右分开显示
    plot1Layout->addWidget(m_pPlot1Toolbar, 0); // 0 是伸缩因子，使工具栏不会被拉伸
    plot2Layout->addWidget(m_pPlot2Toolbar, 0);
    plot3Layout->addWidget(m_pPlot3Toolbar, 0);


    // 布局设置：工具栏2在工具栏1右侧，工具栏3在工具栏1下方
    rightLayout->addWidget(plot1Container, 0, 0, 1, 1); // 工具栏1在左上角
    rightLayout->addWidget(plot2Container, 0, 1, 1, 1); // 工具栏2在右上角
    rightLayout->addWidget(plot3Container, 1, 0, 1, 1); // 工具栏3占满下方两列
    // 设置每列和每行的拉伸因子为1，以确保它们等分
    for (int i = 0; i < 2; ++i) {
        rightLayout->setColumnStretch(i, 1);
        rightLayout->setRowStretch(i, 1);
    }

    // 将右侧布局添加到主布局
    mainLayout1->addLayout(rightLayout, 3); // 右侧占比2


    // 连接按钮和页面切换
    connect(openFileButton, &QPushButton::clicked, [openFileButton, processButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(0); // 切换到文件目录页面
        openFileButton->setChecked(true);
        processButton->setChecked(false);
    });
    connect(processButton, &QPushButton::clicked, [openFileButton, processButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(1); // 切换到 process 功能按钮页面
        openFileButton->setChecked(false);
        processButton->setChecked(true);
    });

    // 默认显示文件目录页面
    stackedWidget->setCurrentIndex(1);
    openFileButton->setChecked(false);
    processButton->setChecked(true);
    connectSignalsAndSlots(); // 连接信号与槽
    //进行裁剪
    connect(hRangeFrom,&QSpinBox::valueChanged,this,&ProcessTab::HFromValueChanged);
    connect(hRangeTo,&QSpinBox::valueChanged,this,&ProcessTab::HToValueChanged);
    connect(vRangeFrom,&QSpinBox::valueChanged,this,&ProcessTab::VFromValueChanged);
    connect(vRangeTo,&QSpinBox::valueChanged,this,&ProcessTab::vToValueChanged);

    // 初始化播放定时器
    playTimer = new QTimer(this);
    connect(playTimer, &QTimer::timeout, this, &ProcessTab::onTimerTimeout);
    playTimer->setInterval(100); // 100ms间隔，相当于10fps

    // 连接播放按钮信号
    connect(playPauseButton, &QPushButton::clicked, this, &ProcessTab::onPlayPauseClicked);

}

// 添加析构函数实现
ProcessTab::~ProcessTab()
{
    // 基类的析构函数会处理共享资源的清理
}

// 连接信号与槽
void ProcessTab::connectSignalsAndSlots() {
    connect(autoAlignButton, &QPushButton::clicked, this,[=]{
        onAutomaticAlignmentClicked(Original_Spectra_Data);});
    connect(manualAlignButton, &QPushButton::clicked, this, &ProcessTab::onManualAlignmentClicked);
    connect(applyCropButton, &QPushButton::clicked, this, &ProcessTab::onApplyCropClicked);
    connect(decayCurveAddButton, &QPushButton::clicked, this, &ProcessTab::onAddDecayCurveClicked);
    connect(spectralCurveAddButton, &QPushButton::clicked, this, &ProcessTab::onAddSpectralCurveClicked);
    connect(autoAlignValue, &QDoubleSpinBox::valueChanged, this, &ProcessTab::automaticAlignment); // 确保正确连接
    connect(resetButton, &QPushButton::clicked, this, &ProcessTab::onResetClicked);
    connect(saveButton, &QPushButton::clicked, this, &ProcessTab::onSaveClicked);
}
void ProcessTab::onProjectOpened(const QString &filePath1) {
    // 如果不需要 filePath1 参数，可以删除该参数或者使用它
}

void ProcessTab::onProjectCreated(const QString &filePath) {
    // 处理创建工程文件的逻辑
}

void ProcessTab::onProjectSaved(const QString &filePath) {
    // 处理保存工程文件的逻辑
}



void ProcessTab::automaticAlignment(int alignmentValue) {
    // 检查是否有数据
    if (Original_Spectra_Data.empty() || Original_Spectra_Data[0].empty()) {
        // 如果没有数据，只保存用户输入的值，不执行对齐操作
        return;
    }

    // 验证必要的数据结构是否已初始化
    if (timeValues.empty()) {
        qWarning() << "timeValues is empty in automaticAlignment";
        return;
    }

    if (scaleFactor <= 0) {
        qWarning() << "scaleFactor not initialized in automaticAlignment";
        return;
    }

    // 获取用户输入的对齐值（纳秒）
    double userAlignValue = autoAlignValue->value();

    // 将用户输入的纳秒时间值转换回数据索引
    int targetIndex = static_cast<int>((userAlignValue * 1000.0) / scaleFactor);

    // 检查边界
    if (targetIndex < 0 || targetIndex >= static_cast<int>(timeValues.size())) {
        QMessageBox::warning(nullptr, "Warning", "Out of boundary range");
        return;
    }

    // 计算需要移动的距离（从当前对齐位置到新位置）
    int shift = targetIndex - currentAlignment;

    // 执行数据移动
    if (shift > 0) {
        shiftDataUp(Original_Spectra_Data, shift);
    } else if (shift < 0) {
        shiftDataDown(Original_Spectra_Data, -shift);
    }

    // 更新当前对齐位置
    currentAlignment = targetIndex;

    // 更新视图
    updateFluorescenceSpectraView(Original_Spectra_Data);

    // 更新 SharedDataManager 中的对齐值
    SharedDataManager::getInstance()->addAlignedData(PlotDataType::FluorescenceMap,
                                                    QVector<GraphData>(),
                                                    userAlignValue,
                                                    TabType::ProcessTab);

    qDebug() << "ProcessTab::automaticAlignment - Data alignment completed";
}

bool ProcessTab::isManualAlignmentSelected() {
    // 实现手动对齐选择状态判断逻辑
    return manualAlignmentSelected;
}


void ProcessTab::cropFluorescenceSpectra(int hRangeFrom, int hRangeTo, int vRangeFrom, int vRangeTo) {

    if (!m_pPlot1) return;

    // 直接设置Plot1的显示范围
    m_pPlot1->xAxis->setRange(vRangeFrom, vRangeTo);  // x轴对应波长
    m_pPlot1->yAxis->setRange(hRangeFrom, hRangeTo);  // y轴对应时间
    m_pPlot1->replot();

    // 更新Plot2和Plot3的显示范围
    if (m_pPlot2) {
        m_pPlot2->xAxis->setRange(hRangeFrom, hRangeTo);  // 时间范围
        m_pPlot2->replot();
    }
    if (m_pPlot3) {
        m_pPlot3->xAxis->setRange(vRangeFrom, vRangeTo);  // 波长范围
        m_pPlot3->replot();
    }

    qDebug() << "ProcessTab::cropFluorescenceSpectra - Data cropping completed";

    // 存储当前坐标轴范围，以便在数据更新时保持不变
    hasCustomAxisRange = true;
    customXRange = m_pPlot1->xAxis->range();
    customYRange = m_pPlot1->yAxis->range();



    // // 隐藏裁剪线
    // if (horizontalLine1) horizontalLine1->setVisible(false);
    // if (horizontalLine2) horizontalLine2->setVisible(false);
    // if (verticalLine1) verticalLine1->setVisible(false);
    // if (verticalLine2) verticalLine2->setVisible(false);

    horizontalLine1->setVisible(false);
    horizontalLine2->setVisible(false);
    verticalLine1->setVisible(false);
    verticalLine2->setVisible(false);

}

void ProcessTab::updateFluorescenceSpectraView(const std::vector<std::vector<int> > &Data) {
    // 如果有自定义坐标轴范围，先保存当前范围
    QCPRange savedXRange, savedYRange;
    bool hasSavedRange = false;

    if (hasCustomAxisRange && m_pPlot1) {
        hasSavedRange = true;
        savedXRange = customXRange;
        savedYRange = customYRange;
    }

    // 根据标志位决定是否调用BaseTab::fillQCustomPlot
    if (!m_dataAlreadyLoaded) {
        // 如果数据还没有被加载，则调用fillQCustomPlot
        qDebug() << "ProcessTab::updateFluorescenceSpectraView - Loading data with fillQCustomPlot";
        BaseTab::fillQCustomPlot(Data, true);
    } else {
        // 如果数据已经被加载，则只处理ProcessTab特有的逻辑
        qDebug() << "ProcessTab::updateFluorescenceSpectraView - Data already loaded, skipping fillQCustomPlot";
        // 重置标志位，为下一次调用做准备
        m_dataAlreadyLoaded = false;
    }

    // 确保colorMap使用最新的timeValues
    if (m_pPlot1) {
        QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
        if (colorMap) {
            // 更新colorMap的y轴范围为当前的timeValues - 添加安全检查
            if (!wavelengthValues.empty() && !timeValues.empty()) {
                colorMap->data()->setRange(QCPRange(wavelengthValues.front(), wavelengthValues.back()),
                                          QCPRange(timeValues.front(), timeValues.back()));
            } else {
                qWarning() << "wavelengthValues or timeValues is empty, cannot set colorMap range";
            }

            // 如果用户输入了对齐值，确保零点位置对应的y坐标是用户输入的值
            double userAlignValue = autoAlignValue->value();
            if (userAlignValue > 0) {
                // 获取当前零点索引
                int zeroIndex = currentAlignment;

                // 确保零点索引有效
                if (zeroIndex >= 0 && zeroIndex < static_cast<int>(timeValues.size())) {
                    // 检查当前零点的y坐标是否与用户输入值匹配
                    double currentZeroY = timeValues[zeroIndex];

                    // 如果不匹配，调整所有y坐标
                    if (std::abs(currentZeroY - userAlignValue) > 0.001) { // 使用小阈值避免浮点误差
                        double offset = userAlignValue - currentZeroY;

                        // 调整所有y坐标
                        for (size_t i = 0; i < timeValues.size(); ++i) {
                            timeValues[i] += offset;
                        }

                        // 更新colorMap的y轴范围 - 添加安全检查
                        if (!wavelengthValues.empty() && !timeValues.empty()) {
                            colorMap->data()->setRange(QCPRange(wavelengthValues.front(), wavelengthValues.back()),
                                                     QCPRange(timeValues.front(), timeValues.back()));
                        }
                    }
                }
            }
        }
    }

    // 如果有保存的坐标轴范围，恢复它们
    if (hasSavedRange && m_pPlot1) {
        m_pPlot1->xAxis->setRange(savedXRange);
        m_pPlot1->yAxis->setRange(savedYRange);

        // 更新Plot2和Plot3的显示范围
        if (m_pPlot2) {
            m_pPlot2->xAxis->setRange(savedYRange);  // 时间范围
        }
        if (m_pPlot3) {
            m_pPlot3->xAxis->setRange(savedXRange);  // 波长范围
        }
    } else {
        // 如果没有保存的范围，使用当前的timeValues设置y轴范围 - 添加安全检查
        if (m_pPlot1 && !timeValues.empty()) {
            m_pPlot1->yAxis->setRange(timeValues.front(), timeValues.back());
        }
    }

    // 强制重绘所有图表
    if (m_pPlot1) m_pPlot1->replot();
    if (m_pPlot2) m_pPlot2->replot();
    if (m_pPlot3) m_pPlot3->replot();

    qDebug() << "ProcessTab::updateFluorescenceSpectraView - Applied custom axis ranges and replotted";

    // 高亮选中的列
    highlightSelectedColumns();

    // 保存数据以供后续使用
    Original_Spectra_Data = Data;

    // 不再需要同步数据到其他标签页
}

void ProcessTab::onMousePress(QMouseEvent* event) {
    try {
        // 首先调用基类的 onMousePress 方法处理基本功能
        BaseTab::onMousePress(event);

        // 检查事件和图表是否有效
        if (!event || !m_pPlot1) {
            return;
        }

        // 如果不是手动对齐模式，则不处理列选择
        if (!manualAlignmentSelected)
        {
            return;
        }

        // 处理 ProcessTab 特有的功能
        if (event->button() == Qt::LeftButton)
        {
            QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (!colorMap) return;

            // 1. 获取原始屏幕坐标映射到的数据坐标（用于十字线和高亮显示）
            int xScreen = static_cast<int>(m_pPlot1->xAxis->pixelToCoord(event->pos().x()));
            int yScreen = static_cast<int>(m_pPlot1->yAxis->pixelToCoord(event->pos().y()));

            // 2. 获取映射到实际数据索引的坐标（用于数据操作）
            double xValue, yValue;
            colorMap->pixelsToCoords(event->pos(), xValue, yValue);
            int dataIndex[2];
            colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

            // 确保坐标在有效范围内
            xScreen = qBound(0, xScreen, static_cast<int>(m_pPlot1->xAxis->range().upper));
            yScreen = qBound(0, yScreen, static_cast<int>(m_pPlot1->yAxis->range().upper));

            int xData = qBound(0, dataIndex[0], static_cast<int>(Original_Spectra_Data.size()) - 1);
            // 使用 xScreen 进行高亮显示
            selectedColumns.clear();
            selectedColumns.insert(xScreen);
            // 存储实际数据索引用于后续操作
            selectedDataIndices.clear();
            selectedDataIndices.insert(xData);
            highlightSelectedColumns();


        }
    } catch (const std::exception &e) {
        qCritical() << "Exception in ProcessTab::onMousePress: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in ProcessTab::onMousePress";
    }
}

void ProcessTab::onMouseMove(QMouseEvent* event) {
    try {
        // 首先调用基类的 onMouseMove 方法处理基本功能
        BaseTab::onMouseMove(event);

        // 检查是否启用了十字线、是否有有效的鼠标事件，以及是否按住了左键
        if (hasHVLine && event && (event->buttons() & Qt::LeftButton)) {
            // 获取鼠标位置对应的数据坐标
            int xScreen = static_cast<int>(m_pPlot1->xAxis->pixelToCoord(event->pos().x()));
            int yScreen = static_cast<int>(m_pPlot1->yAxis->pixelToCoord(event->pos().y()));

            // 确保坐标在有效范围内
            xScreen = qBound(0, xScreen, static_cast<int>(m_pPlot1->xAxis->range().upper));
            yScreen = qBound(0, yScreen, static_cast<int>(m_pPlot1->yAxis->range().upper));

            // 更新 spectralCurveTau 和 decayCurveLambda 的值
            if (spectralCurveTau) {
                spectralCurveTau->setValue(yScreen);
            }
            if (decayCurveLambda) {
                decayCurveLambda->setValue(xScreen);
            }
        }

        // 如果不是手动对齐模式，则不处理列选择
        if (!manualAlignmentSelected) {
            return;
        }

        // 处理 ProcessTab 特有的功能
        if (event->buttons() & Qt::LeftButton) {
            QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (!colorMap) return;

            // 1. 获取原始屏幕坐标映射到的数据坐标（用于十字线和高亮显示）
            int xScreen = static_cast<int>(m_pPlot1->xAxis->pixelToCoord(event->pos().x()));
            int yScreen = static_cast<int>(m_pPlot1->yAxis->pixelToCoord(event->pos().y()));

            // 2. 获取映射到实际数据索引的坐标（用于数据操作）
            double xValue, yValue;
            colorMap->pixelsToCoords(event->pos(), xValue, yValue);
            int dataIndex[2];
            colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

            // 确保坐标在有效范围内
            xScreen = qBound(0, xScreen, static_cast<int>(m_pPlot1->xAxis->range().upper));
            yScreen = qBound(0, yScreen, static_cast<int>(m_pPlot1->yAxis->range().upper));

            int xData = qBound(0, dataIndex[0], static_cast<int>(Original_Spectra_Data.size()) - 1);

            // 使用 xScreen 进行高亮显示
            selectedColumns.insert(xScreen);
            // 存储实际数据索引用于后续操作
            selectedDataIndices.insert(xData);
            highlightSelectedColumns();

        }
    } catch (const std::exception &e) {
        qCritical() << "Exception in ProcessTab::onMouseMove: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in ProcessTab::onMouseMove";
    }
}

void ProcessTab::onMouseRelease(QMouseEvent* event) {
    try {
        // 首先调用基类的 onMouseRelease 方法处理基本功能
        BaseTab::onMouseRelease(event);

        // 检查事件是否有效
        if (!event) {
            return;
        }

        // 然后处理 ProcessTab 特有的功能
        if (event->button() == Qt::LeftButton) {
            // 鼠标释放时，更新选中列的高亮显示
            //highlightSelectedColumns();
        }
    } catch (const std::exception &e) {
        qCritical() << "Exception in ProcessTab::onMouseRelease: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in ProcessTab::onMouseRelease";
    }
}

void ProcessTab::highlightSelectedColumns() {
    // 保存十字线的属性
    bool hadHVLine = hasHVLine;
    double hlinePos = 0;
    double vlinePos = 0;

    // 如果十字线存在，保存它们的位置
    if (hasHVLine && hline && vline) {
        hlinePos = hline->point1->coords().y(); // 保存水平线的y坐标
        vlinePos = vline->point1->coords().x(); // 保存垂直线的x坐标

        // 如果十字线存在，先将其设置为不可见，以避免在clearItems时被删除
        hline->setVisible(false);
        vline->setVisible(false);
    }

    // 仅清除高亮矩形，不清除十字线
    // 首先找出所有高亮矩形和裁剪线，并删除它们
    QList<QCPAbstractItem*> itemsToRemove;
    for (int i = 0; i < m_pPlot1->itemCount(); ++i) {
        QCPAbstractItem* item = m_pPlot1->item(i);
        if (item != hline && item != vline) {
            itemsToRemove.append(item);
        }
    }

    // 删除需要移除的项目
    for (QCPAbstractItem* item : itemsToRemove) {
        m_pPlot1->removeItem(item);
    }

    // 如果之前有十字线，更新其位置并设置为可见
    if (hadHVLine) {
        if (!hline || !vline) {
            // 如果十字线不存在，创建新的
            if (!hline) {
                hline = new QCPItemStraightLine(m_pPlot1);
                hline->setPen(QPen(Qt::red));
            }
            if (!vline) {
                vline = new QCPItemStraightLine(m_pPlot1);
                vline->setPen(QPen(Qt::red));
            }
        }

        // 更新十字线位置
        hline->point1->setCoords(m_pPlot1->xAxis->range().lower, hlinePos);
        hline->point2->setCoords(m_pPlot1->xAxis->range().upper, hlinePos);
        vline->point1->setCoords(vlinePos, m_pPlot1->yAxis->range().lower);
        vline->point2->setCoords(vlinePos, m_pPlot1->yAxis->range().upper);

        // 设置十字线为可见
        hline->setVisible(true);
        vline->setVisible(true);

        hasHVLine = true;
    }

    // 高亮选中的列
    for (int col : selectedColumns) {
        QCPItemRect* rect = new QCPItemRect(m_pPlot1);
        rect->setPen(QPen(Qt::NoPen)); // 不显示边框
        rect->setBrush(QBrush(QColor(65, 105, 225, 80)));
        rect->topLeft->setCoords(col - 0.5, m_pPlot1->yAxis->range().upper);
        rect->bottomRight->setCoords(col + 0.5, m_pPlot1->yAxis->range().lower);
    }

    // 创建或更新裁剪线
    horizontalLine1 = new QCPItemLine(m_pPlot1);
    horizontalLine1->setPen(QPen(Qt::red));
    horizontalLine1->setVisible(false);

    horizontalLine2 = new QCPItemLine(m_pPlot1);
    horizontalLine2->setPen(QPen(Qt::red));
    horizontalLine2->setVisible(false);

    verticalLine1 = new QCPItemLine(m_pPlot1);
    verticalLine1->setPen(QPen(Qt::red));
    verticalLine1->setVisible(false);

    verticalLine2 = new QCPItemLine(m_pPlot1);
    verticalLine2->setPen(QPen(Qt::red));
    verticalLine2->setVisible(false);

    m_pPlot1->replot();
}



void ProcessTab::shiftDataUp(std::vector<std::vector<int> > &data,int shiftCount)
{
    if (data.empty() || data[0].empty()) return;

    int width = data[0].size();
    int shift = shiftCount % width;
    if (shift == 0) return;

    for (auto& row : data) {
        std::vector<int> temp(width);
        for (int i = 0; i < width; ++i) {
            temp[i] = row[(i - shift + width) % width];  // 右移
        }
        row = std::move(temp);
    }
}

void ProcessTab::shiftDataDown(std::vector<std::vector<int> > &data,int shiftCount)
{
    if (data.empty() || data[0].empty()) return;

    int width = data[0].size();
    int shift = shiftCount % width;  // 避免超过列数
    if (shift == 0) return;

    for (auto& row : data) {
        std::vector<int> temp(width);
        for (int i = 0; i < width; ++i) {
            temp[i] = row[(i + shift) % width];  // 左移
        }
        row = std::move(temp);
    }

}

void ProcessTab::addCurve(int value, int delta, CurveType type) {
    // 验证必要的数据结构是否已初始化
    if (timeValues.empty()) {
        QMessageBox::warning(nullptr, "Warning", "Time values not initialized. Please load data first.");
        return;
    }

    if (wavelengthValues.empty()) {
        QMessageBox::warning(nullptr, "Warning", "Wavelength values not initialized. Please load data first.");
        return;
    }

    if (mutableData.empty()) {
        QMessageBox::warning(nullptr, "Warning", "No data available. Please load data first.");
        return;
    }

    // 实现添加曲线的逻辑
    switch (type) {
    case CurveType::Decay:
        // 检查 value 是否在有效范围内
        if (value >= 0 && value < static_cast<int>(mutableData.size())) {
            QVector<double> xData, yData;
            QCPRange yRange = m_pPlot1->yAxis->range();

            if (delta == 0) {
                for (int i = 0; i < numCols && i < static_cast<int>(timeValues.size()); ++i) {
                    double timeValue = timeValues[i];
                    if (timeValue >= yRange.lower && timeValue <= yRange.upper) {
                        xData.append(timeValue);
                        yData.append(mutableData[value][i]);
                    }
                }
            } else {
                for (int i = 0; i < numCols && i < static_cast<int>(timeValues.size()); ++i) {
                    double timeValue = timeValues[i];
                    if (timeValue >= yRange.lower && timeValue <= yRange.upper) {
                        double sum = 0;
                        int count = 0;
                        for (int j = value - delta; j <= value + delta; ++j) {
                            if (j >= 0 && j < static_cast<int>(mutableData.size())) {
                                sum += mutableData[j][i];
                                count++;
                            }
                        }
                        xData.append(timeValue);
                        yData.append(count > 0 ? sum / count : 0);
                    }
                }
            }

            // 创建折线图数据容器
            QCPGraph* decaygraph = m_pPlot2->addGraph();
            decaygraph->setData(xData, yData);
            // 获取波长值并计算对应的颜色
            double wavelength = decayCurveLambda->value();
            // 使用 ThemeManager 获取主题感知的颜色
            QColor curveColor = ThemeManager::getColorForWavelength(wavelength);
            // 设置曲线颜色
            decaygraph->setPen(QPen(curveColor, 2));
            decaygraph->setBrush(QBrush(QColor(curveColor.red(), curveColor.green(), curveColor.blue(), 35)));
            // 设置曲线名称，使用波长和delta值
            QString curveName = QString("λ=%1nm (±%2nm)").arg(decayCurveLambda->value()).arg(delta);
            decaygraph->setName(curveName);

            // 设置自定义属性，标记为用户添加的曲线
            decaygraph->setProperty("isCrosshairCurve", false);
            decaygraph->setProperty("isUserAddedCurve", true);

            // 存储图形和名称的映射关系
            m_graphNameMap[decaygraph] = curveName;

            // 确保图形添加到图例中
            decaygraph->addToLegend();

            // 创建 GraphData 对象并添加到 SharedDataManager
            GraphData curveData;
            curveData.xData = xData;
            curveData.yData = yData;
            curveData.pen = decaygraph->pen();
            curveData.brush = decaygraph->brush();
            curveData.name = curveName;
            curveData.source = DataSource::Process;
            curveData.operationType = DataOperationType::AddedCurve;

            // 添加到 SharedDataManager
            SharedDataManager::getInstance()->addCurve(PlotDataType::DecayCurve, curveData, TabType::ProcessTab);


            // 重绘图表以显示新添加的曲线
            m_pPlot2->replot();

            // 同步Plot2数据到其他标签页
            // 获取当前所有曲线数据
            std::vector<std::vector<int>> decayData;
            int graphCount = m_pPlot2->graphCount();
            decayData.resize(graphCount); // 每个图表一行数据

            // // 遍历所有图表，收集数据
            // for (int graphIndex = 0; graphIndex < graphCount; ++graphIndex) {
            //     QCPGraph* graph = m_pPlot2->graph(graphIndex);
            //     if (graph) {
            //         decayData[graphIndex].resize(dataSize);
            //         for (int i = 0; i < dataSize; ++i) {
            //             // 如果点存在，获取其值，否则设为0
            //             if (i < graph->dataCount()) {
            //                 decayData[graphIndex][i] = static_cast<int>(graph->data()->at(i)->value);
            //             } else {
            //                 decayData[graphIndex][i] = 0;
            //             }
            //         }
            //     }
            // }

            // 不再需要同步数据到其他标签页

        } else {
            QMessageBox::warning(nullptr, "警告", "行索引超出范围");
        }
        break;
    case CurveType::Spectral:
        qInfo() << "Spectral Curve" ;
        // 检查 value 是否在有效范围内
        if (value >= 0 && value < static_cast<int>(mutableData[0].size())) {
            // 不再需要生成随机颜色，因为我们将使用固定的颜色映射
            QVector<double> xData, yData;
            QCPRange xRange = m_pPlot1->xAxis->range();

            if (delta == 0) {
                for (int i = 0; i < numRows && i < static_cast<int>(wavelengthValues.size()); ++i) {
                    double wavelengthValue = wavelengthValues[i];
                    if (wavelengthValue >= xRange.lower && wavelengthValue <= xRange.upper) {
                        xData.append(wavelengthValue);
                        yData.append(mutableData[i][value]);
                    }
                }
            } else {
                for (int i = 0; i < numRows && i < static_cast<int>(wavelengthValues.size()); ++i) {
                    double wavelengthValue = wavelengthValues[i];
                    if (wavelengthValue >= xRange.lower && wavelengthValue <= xRange.upper) {
                        double sum = 0;
                        int count = 0;
                        for (int j = value - delta; j <= value + delta; ++j) {
                            if (j >= 0 && j < static_cast<int>(mutableData[0].size())) {
                                sum += mutableData[i][j];
                                count++;
                            }
                        }
                        xData.append(wavelengthValue);
                        yData.append(count > 0 ? sum / count : 0);
                    }
                }
            }

            // 创建光谱曲线图
            QCPGraph* spectralGraph = m_pPlot3->addGraph();
            spectralGraph->setData(xData, yData);

            // 获取时间值并计算对应的颜色
            double tau = spectralCurveTau->value();
            // 使用 ThemeManager 获取主题感知的颜色
            QColor curveColor = ThemeManager::getColorForTime(tau);

            // 设置曲线颜色
            spectralGraph->setPen(QPen(curveColor, 2));
            spectralGraph->setBrush(QBrush(QColor(curveColor.red(), curveColor.green(), curveColor.blue(), 30)));
            // 设置曲线名称，使用时间和delta值
            QString curveName = QString("t=%1ns (±%2ns)").arg(spectralCurveTau->value()).arg(delta);
            spectralGraph->setName(curveName);

            // 设置自定义属性，标记为用户添加的曲线
            spectralGraph->setProperty("isCrosshairCurve", false);
            spectralGraph->setProperty("isUserAddedCurve", true);

            // 存储图形和名称的映射关系
            m_graphNameMap[spectralGraph] = curveName;

            // 确保图形添加到图例中
            spectralGraph->addToLegend();

            // 创建 GraphData 对象并添加到 SharedDataManager
            GraphData curveData;
            curveData.xData = xData;
            curveData.yData = yData;
            curveData.pen = spectralGraph->pen();
            curveData.brush = spectralGraph->brush();
            curveData.name = curveName;
            curveData.source = DataSource::Process;
            curveData.operationType = DataOperationType::AddedCurve;

            // 添加到 SharedDataManager
            SharedDataManager::getInstance()->addCurve(PlotDataType::SpectralCurve, curveData, TabType::ProcessTab);


            // 自动调整Y轴范围
            m_pPlot3->rescaleAxes();
            // 重绘图表以显示新添加的曲线
            m_pPlot3->replot();

            // 同步Plot3数据到其他标签页
            // 获取当前所有曲线数据
            std::vector<std::vector<int>> spectralData;
            int graphCount = m_pPlot3->graphCount();
            int dataSize = Original_Spectra_Data.size();
            spectralData.resize(graphCount); // 每个图表一行数据

            // 遍历所有图表，收集数据
            for (int graphIndex = 0; graphIndex < graphCount; ++graphIndex) {
                QCPGraph* graph = m_pPlot3->graph(graphIndex);
                if (graph) {
                    spectralData[graphIndex].resize(dataSize);
                    for (int i = 0; i < dataSize; ++i) {
                        // 如果点存在，获取其值，否则设为0
                        if (i < graph->dataCount()) {
                            spectralData[graphIndex][i] = static_cast<int>(graph->data()->at(i)->value);
                        } else {
                            spectralData[graphIndex][i] = 0;
                        }
                    }
                }
            }

            // 不再需要同步数据到其他标签页

        } else {
            QMessageBox::warning(nullptr, "警告", "列索引超出范围");
        }
        break;
    default:
        return;
    }

}

void ProcessTab::enableFrameSlider() {
    // 启用帧滑块的逻辑
}

void ProcessTab::disableFrameSlider() {
    // 禁用帧滑块的逻辑
}

// 添加自动对齐按钮点击事件处理函数
void ProcessTab::onAutomaticAlignmentClicked(std::vector<std::vector<int>>& data) {

    if (!data.empty() && !data[0].empty()) {
        // 验证必要的数据结构是否已初始化
        if (timeValues.empty()) {
            QMessageBox::warning(nullptr, "Warning", "Time values not initialized. Please load data first.");
            return;
        }

        if (scaleFactor <= 0) {
            QMessageBox::warning(nullptr, "Warning", "Scale factor not initialized. Please load data first.");
            return;
        }

        // 断开之前的连接以避免触发信号
        disconnect(autoAlignValue, &QDoubleSpinBox::valueChanged, this, &ProcessTab::automaticAlignment);

        // 保存用户当前输入的值
        double userInputValue = autoAlignValue->value();
        bool hasUserInput = (userInputValue > 0);

        // 第一步：始终找到统一的零点并对齐所有数据
        int unifiedZero = findUnifiedZeroPoint(data);

        // 对数据进行对齐（无论用户是否输入了值，都先对齐所有零点）
        alignData(data, unifiedZero);

        // 更新当前对齐位置
        currentAlignment = unifiedZero;

        // 将数据索引转换为对应的时间值
        double timeValue = (static_cast<double>(unifiedZero) * scaleFactor) / 1000.0;

        // 更新 autoAlignValue 的范围 - 添加安全检查
        if (!timeValues.empty()) {
            autoAlignValue->setRange(0, static_cast<int>(timeValues.back())); // 设置范围为0到最大时间值
        } else {
            qWarning() << "timeValues is empty, cannot set range";
            return;
        }

        // 第二步：如果用户输入了值，则将已对齐的零点移动到用户指定的位置
        if (hasUserInput) {
            // 将用户输入的纳秒时间值转换回数据索引
            int targetIndex = static_cast<int>((userInputValue * 1000.0) / scaleFactor);

            // 计算需要移动的距离（从当前已对齐的零点到用户指定位置）
            int shift = targetIndex - unifiedZero;

            // 执行数据移动
            if (shift > 0) {
                shiftDataUp(data, shift);
            } else if (shift < 0) {
                shiftDataDown(data, -shift);
            }

            // 更新当前对齐位置为用户指定的位置
            currentAlignment = targetIndex;
        } else {
            // 如果用户没有输入值，则使用计算出的时间值
            autoAlignValue->setValue(static_cast<double>(timeValue)); // 转换为纳秒
        }

        // 更新视图
        updateFluorescenceSpectraView(data);

        // 重新连接信号
        connect(autoAlignValue, &QDoubleSpinBox::valueChanged, this, &ProcessTab::automaticAlignment);
    } else {
        // 即使没有数据，也保持输入框可用
        // 但不执行对齐操作
        QMessageBox::warning(nullptr, "Warning", "Data is Empty");
    }
}

// 添加手动对齐按钮点击事件处理函数
void ProcessTab::onManualAlignmentClicked() {
    // 检查manualAlignButton是否为空
    if (!manualAlignButton) {
        qWarning() << "manualAlignButton is null in onManualAlignmentClicked";
        return;
    }

    if(!Original_Spectra_Data.empty() && !Original_Spectra_Data[0].empty())
    {
        manualAlignmentSelected = !manualAlignmentSelected;

        // 根据状态设置按钮颜色和字体
        if (manualAlignmentSelected) {
            // Enable align mode - disable synchronization with other tabs
            //enableAlignMode(true);

            // 设置按钮为红色背景，字体为白色
            manualAlignButton->setStyleSheet("QPushButton { background-color: #FF6666; color: white; }");
            // 进入手动对齐状态
            manualAlignment();
        } else {
            // Disable align mode - re-enable synchronization with other tabs
            //enableAlignMode(false);

            // 退出手动对齐状态
            quitmanualAlignment();
        }
    }
    else
    {
        QMessageBox::warning(nullptr, "警告", "请先进行手动对齐");
    }
}

void ProcessTab::manualAlignment() {
    // 先断开之前的连接，避免重复连接
    disconnect(m_pPlot1, &QCustomPlot::mousePress, this, &ProcessTab::onMousePress);
    disconnect(m_pPlot1, &QCustomPlot::mouseMove, this, &ProcessTab::onMouseMove);
    disconnect(m_pPlot1, &QCustomPlot::mouseRelease, this, &ProcessTab::onMouseRelease);
    disconnect(crossControl, &CrossControl::buttonClicked, this, &ProcessTab::onButtonClicked);

    // 重新连接鼠标事件
    connect(m_pPlot1, &QCustomPlot::mousePress, this, &ProcessTab::onMousePress);
    connect(m_pPlot1, &QCustomPlot::mouseMove, this, &ProcessTab::onMouseMove);
    connect(m_pPlot1, &QCustomPlot::mouseRelease, this, &ProcessTab::onMouseRelease);

    // 连接 CrossControl 的信号到 ProcessTab 的槽函数
    connect(crossControl, &CrossControl::buttonClicked, this, &ProcessTab::onButtonClicked);

    // 启用范围输入控件
    hRangeFrom->setEnabled(true);
    hRangeTo->setEnabled(true);
    vRangeFrom->setEnabled(true);
    vRangeTo->setEnabled(true);
}

void ProcessTab::quitmanualAlignment()
{
    // 检查manualAlignButton是否为空
    if (manualAlignButton) {
        manualAlignButton->setStyleSheet("");
    } else {
        qWarning() << "manualAlignButton is null in quitmanualAlignment";
    }

    // 清除之前的高亮显示
    if (m_pPlot1) {
        m_pPlot1->clearItems();
        m_pPlot1->replot();
        disconnect(m_pPlot1, &QCustomPlot::mousePress, this, &ProcessTab::onMousePress);
        disconnect(m_pPlot1, &QCustomPlot::mouseMove, this, &ProcessTab::onMouseMove);
        disconnect(m_pPlot1, &QCustomPlot::mouseRelease, this, &ProcessTab::onMouseRelease);
    }

    if (crossControl) {
        disconnect(crossControl, &CrossControl::buttonClicked, this, &ProcessTab::onButtonClicked);
    }

    if (m_pPlot1) {
        horizontalLine1 = new QCPItemLine(m_pPlot1);
        horizontalLine1->setPen(QPen(Qt::red));
        horizontalLine1->setVisible(false);

        horizontalLine2 = new QCPItemLine(m_pPlot1);
        horizontalLine2->setPen(QPen(Qt::red));
        horizontalLine2->setVisible(false);

        verticalLine1 = new QCPItemLine(m_pPlot1);
        verticalLine1->setPen(QPen(Qt::red));
        verticalLine1->setVisible(false);

        verticalLine2 = new QCPItemLine(m_pPlot1);
        verticalLine2->setPen(QPen(Qt::red));
        verticalLine2->setVisible(false);
    }
}

// 添加裁剪功能
void ProcessTab::applyCrop() {
    int ihRangeFrom = hRangeFrom->value();
    int ihRangeTo = hRangeTo->value();
    int ivRangeFrom = vRangeFrom->value();
    int ivRangeTo = vRangeTo->value();

    // 裁剪荧光光谱图数据
    cropFluorescenceSpectra(ihRangeFrom, ihRangeTo, ivRangeFrom, ivRangeTo);
}

void ProcessTab::HFromValueChanged(int hRangeFrom)
{
    if (hRangeFrom >= hRangeTo->value()) {
        QMessageBox::warning(nullptr, "警告", "To 值必须大于 From 值");
        return;
    }
    horizontalLine1->setVisible(true);
    int ihRangeTo = hRangeTo->value();
    int ivRangeFrom = vRangeFrom->value();
    int ivRangeTo = vRangeTo->value();
    updateLinePositions(hRangeFrom, ihRangeTo, ivRangeFrom, ivRangeTo);
}

void ProcessTab::HToValueChanged(int hRangeTo)
{
    if (hRangeTo <= hRangeFrom->value()) {
        QMessageBox::warning(nullptr, "警告", "To 值必须大于 From 值");
        return;
    }
    horizontalLine2->setVisible(true);
    int ihRangeFrom = hRangeFrom->value();
    int ivRangeFrom = vRangeFrom->value();
    int ivRangeTo = vRangeTo->value();
    updateLinePositions(ihRangeFrom, hRangeTo, ivRangeFrom, ivRangeTo);

}

void ProcessTab::VFromValueChanged(int vRangeFrom)
{
    if (vRangeFrom >= vRangeTo->value()) {
        QMessageBox::warning(nullptr, "警告", "From 值必须小于 To 值");
        return;
    }
    verticalLine1->setVisible(true);
    int ihRangeFrom = hRangeFrom->value();
    int ihRangeTo = hRangeTo->value();
    int ivRangeTo = vRangeTo->value();
    updateLinePositions(ihRangeFrom, ihRangeTo, vRangeFrom, ivRangeTo);
}

void ProcessTab::vToValueChanged(int vRangeTo)
{
    if (vRangeTo <= vRangeFrom->value()) {
        QMessageBox::warning(nullptr, "警告", "To 值必须大于 From 值");
        return;
    }
    verticalLine2->setVisible(true);
    int ihRangeFrom = hRangeFrom->value();
    int ihRangeTo = hRangeTo->value();
    int ivRangeFrom = vRangeFrom->value();
    updateLinePositions(ihRangeFrom, ihRangeTo, ivRangeFrom, vRangeTo);


}

void ProcessTab::updateLinePositions(int hRangeFrom, int hRangeTo, int vRangeFrom, int vRangeTo)
{
    // 更新水平红线1
    if (!verticalLine1->visible() && !verticalLine2->visible()) {
        horizontalLine1->start->setCoords(m_pPlot1->xAxis->range().lower, hRangeFrom);
        horizontalLine1->end->setCoords(m_pPlot1->xAxis->range().upper, hRangeFrom);
        horizontalLine1->setLayer("overlay");
    } else if (verticalLine1->visible() && !verticalLine2->visible()) {
        horizontalLine1->start->setCoords(vRangeFrom, hRangeFrom);
        horizontalLine1->end->setCoords(m_pPlot1->xAxis->range().upper, hRangeFrom);
        horizontalLine1->setLayer("overlay");
    } else if (!verticalLine1->visible() && verticalLine2->visible()) {
        horizontalLine1->start->setCoords(m_pPlot1->xAxis->range().lower, hRangeFrom);
        horizontalLine1->end->setCoords(vRangeTo, hRangeFrom);
        horizontalLine1->setLayer("overlay");
    } else {
        horizontalLine1->start->setCoords( vRangeFrom, hRangeFrom);
        horizontalLine1->end->setCoords(vRangeTo, hRangeFrom);
        horizontalLine1->setLayer("overlay");
    }

    // 更新水平红线2
    if (!verticalLine1->visible() && !verticalLine2->visible()) {
        horizontalLine2->start->setCoords(m_pPlot1->xAxis->range().lower, hRangeTo);
        horizontalLine2->end->setCoords(m_pPlot1->xAxis->range().upper, hRangeTo);
        horizontalLine2->setLayer("overlay");
    } else if (verticalLine1->visible() && !verticalLine2->visible()) {
        horizontalLine2->start->setCoords(vRangeFrom, hRangeTo);
        horizontalLine2->end->setCoords(m_pPlot1->xAxis->range().upper, hRangeTo);
        horizontalLine2->setLayer("overlay");
    } else if (!verticalLine1->visible() && verticalLine2->visible()) {
        horizontalLine2->start->setCoords(m_pPlot1->xAxis->range().lower, hRangeTo);
        horizontalLine2->end->setCoords(vRangeTo, hRangeTo);
        horizontalLine2->setLayer("overlay");
    } else {
        horizontalLine2->start->setCoords(vRangeFrom, hRangeTo);
        horizontalLine2->end->setCoords(vRangeTo, hRangeTo);
        horizontalLine2->setLayer("overlay");
    }

    // 更新垂直红线1
    if (!horizontalLine1->visible() && !horizontalLine2->visible()) {
        verticalLine1->start->setCoords(vRangeFrom, m_pPlot1->yAxis->range().lower);
        verticalLine1->end->setCoords(vRangeFrom, m_pPlot1->yAxis->range().upper);
        verticalLine1->setLayer("overlay");
    } else if (horizontalLine1->visible() && !horizontalLine2->visible()) {
        verticalLine1->start->setCoords(vRangeFrom, hRangeFrom);
        verticalLine1->end->setCoords(vRangeFrom, m_pPlot1->yAxis->range().upper);
        verticalLine1->setLayer("overlay");
    } else if (!horizontalLine1->visible() && horizontalLine2->visible()) {
        verticalLine1->start->setCoords(vRangeFrom, m_pPlot1->yAxis->range().lower);
        verticalLine1->end->setCoords(vRangeFrom, hRangeTo);
        verticalLine1->setLayer("overlay");
    } else {
        verticalLine1->start->setCoords(vRangeFrom, hRangeFrom);
        verticalLine1->end->setCoords(vRangeFrom, hRangeTo);
        verticalLine1->setLayer("overlay");
    }

    // 更新垂直红线2
    if (!horizontalLine1->visible() && !horizontalLine2->visible()) {
        verticalLine2->start->setCoords(vRangeTo, m_pPlot1->yAxis->range().lower);
        verticalLine2->end->setCoords(vRangeTo, m_pPlot1->yAxis->range().upper);
        verticalLine2->setLayer("overlay");
    } else if (horizontalLine1->visible() && !horizontalLine2->visible()) {
        verticalLine2->start->setCoords(vRangeTo, hRangeFrom);
        verticalLine2->end->setCoords(vRangeTo, m_pPlot1->yAxis->range().upper);
        verticalLine2->setLayer("overlay");
    } else if (!horizontalLine1->visible() && horizontalLine2->visible()) {
        verticalLine2->start->setCoords(vRangeTo, m_pPlot1->yAxis->range().lower);
        verticalLine2->setLayer("overlay");
        verticalLine2->end->setCoords(vRangeTo, hRangeTo);
    } else {
        verticalLine2->start->setCoords(vRangeTo, hRangeFrom);
        verticalLine2->end->setCoords(vRangeTo, hRangeTo);
        verticalLine2->setLayer("overlay");
    }

    m_pPlot1->replot();
}

// 添加曲线添加功能
void ProcessTab::addDecayCurve() {
    // 获取当前值
    int screenLambda = decayCurveLambda->value();
    int delta = decayCurveDelta->value();

    // 获取 colorMap 对象
    QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
    if (!colorMap) return;

    // 将屏幕坐标转换为数据索引
    double xValue = screenLambda;
    double yValue = 0; // 不需要 y 值，因为我们只需要 x 方向的索引
    int dataIndex[2];
    colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

    // 确保数据索引在有效范围内
    int lambda = qBound(0, dataIndex[0], static_cast<int>(mutableData.size()) - 1);

    addCurve(lambda, delta, CurveType::Decay);


}

void ProcessTab::addSpectralCurve() {
    // 获取当前值
    int screenTau = spectralCurveTau->value();
    int delta = spectralCurveDelta->value();

    // 获取 colorMap 对象
    QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
    if (!colorMap) return;

    // 将屏幕坐标转换为数据索引
    double xValue = 0; // 不需要 x 值，因为我们只需要 y 方向的索引
    double yValue = screenTau;
    int dataIndex[2];
    colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

    // 确保数据索引在有效范围内
    int tau = qBound(0, dataIndex[1], static_cast<int>(mutableData[0].size()) - 1);

    addCurve(tau, delta, CurveType::Spectral);

    // int tau = spectralCurveTau->value();
    // int delta = spectralCurveDelta->value();
    // addCurve(tau, delta, CurveType::Spectral);
}

void ProcessTab::onCurveVisibilityChanged(bool checked, QCPGraph *graph)
{
    if (graph) {
        graph->setVisible(checked);
        m_pPlot2->replot(); // 重新绘制图表以反映更改
        m_pPlot3->replot();
    }
}

void ProcessTab::onLegendClick(QCPLegend* legend, QCPAbstractLegendItem* item, QMouseEvent* event) {
    // 忽略右键点击
    if (event->button() == Qt::RightButton)
        return;

    // 尝试将图例项转换为 QCPPlottableLegendItem
    QCPPlottableLegendItem* plottableItem = qobject_cast<QCPPlottableLegendItem*>(item);
    if (!plottableItem)
        return;

    // 获取关联的图形
    QCPAbstractPlottable* plottable = plottableItem->plottable();
    QCPGraph* graph = qobject_cast<QCPGraph*>(plottable);
    if (!graph)
        return;

    // 切换图形的可见性
    bool visible = !graph->visible();
    graph->setVisible(visible);

    // 修改图例项的样式以显示可见性状态
    if (visible) {
        // 图形可见 - 使用正常样式
        plottableItem->setTextColor(ThemeManager::getTextColor());
        plottableItem->setFont(QFont(plottableItem->font().family(), plottableItem->font().pointSize(), QFont::Normal));
    } else {
        // 图形隐藏 - 使用灰色和删除线样式
        plottableItem->setTextColor(QColor(150, 150, 150)); // 灰色文本
        QFont strikeOutFont = plottableItem->font();
        strikeOutFont.setStrikeOut(true); // 添加删除线
        plottableItem->setFont(strikeOutFont);
    }

    // 重绘图表
    if (legend->parentPlot()) {
        legend->parentPlot()->replot();
    }
}


// void ProcessTab::onSplitFramesClicked() {
//     bool isSplit = splitFramesButton->isChecked();
//     if (isSplit) {
//         enableFrameSlider();
//         splitFrameDetailsWidget->show(); // 显示布局
//     } else {
//         disableFrameSlider();
//         splitFrameDetailsWidget->hide(); // 隐藏布局
//     }
// }

// 添加 undoCrop 函数实现
void ProcessTab::undoCrop() {
    // 撤销裁剪操作的逻辑
    // 可以在这里恢复裁剪前的数据或状态
}

// 添加 onApplyCropClicked 函数实现
void ProcessTab::onApplyCropClicked() {
    applyCrop();
}

// 添加 onUndoCropClicked 函数实现
void ProcessTab::onUndoCropClicked() {
    undoCrop();
}

// 添加 onAddDecayCurveClicked 函数实现
void ProcessTab::onAddDecayCurveClicked() {
    addDecayCurve();
}

// 添加 onAddSpectralCurveClicked 函数实现
void ProcessTab::onAddSpectralCurveClicked() {
    addSpectralCurve();
}


void ProcessTab::onCurveSelected() {
    // 处理曲线选择事件的逻辑
}

void ProcessTab::onUndoSplitClicked() {
    // 处理曲线选择事件的逻辑
}

// 添加 onAlignmentChanged 函数实现
void ProcessTab::onAlignmentChanged() {
    // 处理对齐变化事件的逻辑
}

// 按钮点击处理函数
void ProcessTab::onPlusClicked() {
    // 处理 Plus 按钮点击事件
}

// 重置按钮点击处理函数
void ProcessTab::onResetClicked() {
    // 重置自定义坐标轴范围
    hasCustomAxisRange = false;

    // 如果有数据，重新加载并使用默认范围
    if (!Original_Spectra_Data.empty() && !Original_Spectra_Data[0].empty()) {
        updateFluorescenceSpectraView(Original_Spectra_Data);
    }

    // 调用基类的reset方法重置其他设置
    BaseTab::reset();
}

// 保存按钮点击处理函数
void ProcessTab::onSaveClicked() {
    // 获取当前选中的原始文件名
    QString originalFileName = getCurrentSelectedFileName();
    if (originalFileName.isEmpty()) {
        QMessageBox::warning(this, "Save Failed", "Please select an original file first");
        return;
    }

    // 检查是否有数据需要保存
    if (!m_pPlot1 || !m_pPlot2 || !m_pPlot3) {
        QMessageBox::warning(this, "Save Failed", "Plot widgets are not initialized");
        return;
    }

    // 检查是否有实际数据
    if (Original_Spectra_Data.empty()) {
        QMessageBox::warning(this, "Save Failed", "No data available to save");
        return;
    }

    // 使用ProcessAnalysisDataHandler保存数据
    ProcessAnalysisDataHandler* handler = ProcessAnalysisDataHandler::getInstance();

    // 执行保存
    bool success = handler->saveProcessData(m_pPlot1, m_pPlot2, m_pPlot3, originalFileName);

    if (success) {
        // 显示保存成功消息
        QMessageBox::information(this, "Save Successful", "Process data has been saved successfully");

        qDebug() << "ProcessTab: Successfully saved data for file:" << originalFileName;
        // 注意：markAsSaved已在saveProcessData()中调用，无需重复调用
    } else {
        QMessageBox::critical(this, "Save Failed", "Error occurred while saving Process data. Please check file permissions and disk space");
        qCritical() << "ProcessTab: Failed to save data for file:" << originalFileName;
    }
}
// 删除 onZoomInClicked 函数，因为它没有在 ProcessTab.h 中声明
void ProcessTab::onButtonClicked(const QString &name) {
    // 设置移动步长为5，使每次移动更明显
    const int moveSteps = 5;

    if (name == "Top")
    {
        if (!selectedDataIndices.empty())
        {
            for (auto col : selectedDataIndices)
            {
                if (col < Original_Spectra_Data.size())
                {
                    auto &columnData = Original_Spectra_Data[col];
                    if (!columnData.empty())
                    {
                        // 移动多个元素，增加步长
                        for (int i = 0; i < moveSteps; i++) {
                            int lastElement = columnData.back();
                            columnData.pop_back();
                            columnData.insert(columnData.begin(), lastElement);
                        }
                    }
                }
            }
            updateFluorescenceSpectraView(Original_Spectra_Data);

            qDebug() << "ProcessTab::onButtonClicked(Top) - Manual alignment data shift completed";
        }
        else
        {
            QMessageBox::warning(nullptr, "Warning", "No columns selected.");
            return;
        }
    }
    else if (name == "Bottom")
    {
        if (!selectedDataIndices.empty())
        {
            for (auto col : selectedDataIndices)
            {
                if (col < Original_Spectra_Data.size())
                {
                    auto &columnData = Original_Spectra_Data[col];
                    if (!columnData.empty())
                    {
                        // 移动多个元素，增加步长
                        for (int i = 0; i < moveSteps; i++) {
                            int firstElement = columnData.front();
                            columnData.erase(columnData.begin());
                            columnData.push_back(firstElement);
                        }
                    }
                }
            }
            updateFluorescenceSpectraView(Original_Spectra_Data);

            qDebug() << "ProcessTab::onButtonClicked(Bottom) - Manual alignment data shift completed";
        }
        else
        {
            QMessageBox::warning(nullptr, "Warning", "No columns selected.");
            return;
        }
    }
    else if (name == "Left")
    {
        // 处理 Left 按钮点击事件 - 将选择移动到左侧列
        if (!selectedDataIndices.empty() && !selectedColumns.empty())
        {
            // 创建新的选择集合
            std::set<int> newSelectedDataIndices;
            std::set<int> newSelectedColumns;

            // 对每个选中的列，选择其左侧的列
            for (auto col : selectedDataIndices)
            {
                // 确保不会超出左边界
                if (col > 0 && col < Original_Spectra_Data.size())
                {
                    newSelectedDataIndices.insert(col - 1);
                }
            }

            // 对每个屏幕列，选择其左侧的列
            for (auto col : selectedColumns)
            {
                // 确保不会超出左边界
                if (col > 0)
                {
                    newSelectedColumns.insert(col - 1);
                }
            }

            // 更新选择
            if (!newSelectedDataIndices.empty() && !newSelectedColumns.empty())
            {
                selectedDataIndices = newSelectedDataIndices;
                selectedColumns = newSelectedColumns;
                highlightSelectedColumns();
            }
        }
        else
        {
            QMessageBox::warning(nullptr, "Warning", "No columns selected.");
            return;
        }
    }
    else if (name == "Right")
    {
        // 处理 Right 按钮点击事件 - 将选择移动到右侧列
        if (!selectedDataIndices.empty() && !selectedColumns.empty())
        {
            // 创建新的选择集合
            std::set<int> newSelectedDataIndices;
            std::set<int> newSelectedColumns;

            // 对每个选中的列，选择其右侧的列
            for (auto col : selectedDataIndices)
            {
                // 确保不会超出右边界
                if (col < Original_Spectra_Data.size() - 1)
                {
                    newSelectedDataIndices.insert(col + 1);
                }
            }

            // 对每个屏幕列，选择其右侧的列
            for (auto col : selectedColumns)
            {
                // 确保不会超出右边界
                if (col < static_cast<int>(m_pPlot1->xAxis->range().upper) - 1)
                {
                    newSelectedColumns.insert(col + 1);
                }
            }

            // 更新选择
            if (!newSelectedDataIndices.empty() && !newSelectedColumns.empty())
            {
                selectedDataIndices = newSelectedDataIndices;
                selectedColumns = newSelectedColumns;
                highlightSelectedColumns();
            }
        }
        else
        {
            QMessageBox::warning(nullptr, "Warning", "No columns selected.");
            return;
        }
    }
}

void ProcessTab::onFileSelected(int row, const QString &filePath)
{
    // 添加调试信息
    qDebug() << "ProcessTab::onFileSelected called with row:" << row << "filePath:" << filePath;

    // 清除 SharedDataManager 中的数据
    SharedDataManager::getInstance()->clearTabData(TabType::ProcessTab);

    // 清除裁剪线对象
    if (horizontalLine1) {
        m_pPlot1->removeItem(horizontalLine1);
        horizontalLine1 = nullptr;
    }
    if (horizontalLine2) {
        m_pPlot1->removeItem(horizontalLine2);
        horizontalLine2 = nullptr;
    }
    if (verticalLine1) {
        m_pPlot1->removeItem(verticalLine1);
        verticalLine1 = nullptr;
    }
    if (verticalLine2) {
        m_pPlot1->removeItem(verticalLine2);
        verticalLine2 = nullptr;
    }

    // 重置手动对齐状态
    if (manualAlignmentSelected) {
        quitmanualAlignment();
        manualAlignmentSelected = false;
    }

    // 清除图例
    if (m_pPlot2 && m_pPlot2->legend) {
        m_pPlot2->legend->clearItems();
    }
    if (m_pPlot3 && m_pPlot3->legend) {
        m_pPlot3->legend->clearItems();
    }

    // 清除图形名称映射
    m_graphNameMap.clear();

    // 设置标志位，表示数据将被加载
    m_dataAlreadyLoaded = true;

    // ProcessAnalysisSave 功能：ProcessTab不再需要自动保存功能

    // 调用基类的onFileSelected方法
    BaseTab::onFileSelected(row, filePath);

    // 重置自定义坐标轴范围，因为加载新文件时应该使用默认范围
    hasCustomAxisRange = false;

    // 然后执行 ProcessTab 特有的功能
    QFileInfo processTabFileInfo(filePath);
    QString fileName = processTabFileInfo.fileName();
    ProjectFileManager* fileManager = ProjectFileManager::getInstance();

    // 根据文件类型使用不同的数据处理器（遵循BaseTab::onFileSelected模式）
    if (fileName.endsWith(".sfd")) {
        // 原始文件：使用 MeasureDataHandler
        MeasureDataHandler* handler = fileManager->getHandler(fileName);
        if (handler) {
            std::vector<std::vector<int>> Data = handler->getDataArrayTotal();

            // 添加边界检查
            if (!Data.empty() && !Data[0].empty()) {
                updateFluorescenceSpectraView(Data);
                initGlobalVariables();
            }
        } else {
            qWarning() << "ProcessTab: No MeasureDataHandler found for .sfd file:" << fileName;
        }
    }
    else if (fileName.endsWith(".sfdp")) {
        // 处理文件：使用 ProcessAnalysisDataHandler
        // 注意：.sfdp文件已经在BaseTab::onFileSelected中加载到plots，
        // 这里我们需要从plots中获取数据来更新ProcessTab特有的视图
        qDebug() << "ProcessTab: .sfdp file loaded by BaseTab, extracting data for ProcessTab views";

        // 从Plot1中提取数据用于updateFluorescenceSpectraView
        if (m_pPlot1 && m_pPlot1->plottableCount() > 0) {
            // 尝试从colormap中提取数据
            QCPColorMap* colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (colorMap && colorMap->data()) {
                int keySize = colorMap->data()->keySize();
                int valueSize = colorMap->data()->valueSize();

                if (keySize > 0 && valueSize > 0) {
                    // 构建兼容的数据格式
                    std::vector<std::vector<int>> Data(keySize, std::vector<int>(valueSize));

                    for (int i = 0; i < keySize; ++i) {
                        for (int j = 0; j < valueSize; ++j) {
                            Data[i][j] = static_cast<int>(colorMap->data()->cell(i, j));
                        }
                    }

                    updateFluorescenceSpectraView(Data);
                    initGlobalVariables();
                    qDebug() << "ProcessTab: Successfully extracted data from .sfdp colormap";
                } else {
                    qWarning() << "ProcessTab: Invalid colormap dimensions for .sfdp file";
                }
            } else {
                qWarning() << "ProcessTab: No colormap found in Plot1 for .sfdp file";
            }
        } else {
            qWarning() << "ProcessTab: No graph data available in Plot1 for .sfdp file";
        }
    }
    else if (fileName.endsWith(".sfda")) {
        // 分析文件：使用 ProcessAnalysisDataHandler
        // 注意：.sfda文件已经在BaseTab::onFileSelected中加载到plots，
        // 这里我们需要从plots中获取数据来更新ProcessTab特有的视图
        qDebug() << "ProcessTab: .sfda file loaded by BaseTab, extracting data for ProcessTab views";

        // 从Plot1中提取数据用于updateFluorescenceSpectraView
        if (m_pPlot1 && m_pPlot1->plottableCount() > 0) {
            // 尝试从colormap中提取数据
            QCPColorMap* colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (colorMap && colorMap->data()) {
                int keySize = colorMap->data()->keySize();
                int valueSize = colorMap->data()->valueSize();

                if (keySize > 0 && valueSize > 0) {
                    // 构建兼容的数据格式
                    std::vector<std::vector<int>> Data(keySize, std::vector<int>(valueSize));

                    for (int i = 0; i < keySize; ++i) {
                        for (int j = 0; j < valueSize; ++j) {
                            Data[i][j] = static_cast<int>(colorMap->data()->cell(i, j));
                        }
                    }

                    updateFluorescenceSpectraView(Data);
                    initGlobalVariables();
                    qDebug() << "ProcessTab: Successfully extracted data from .sfda colormap";
                } else {
                    qWarning() << "ProcessTab: Invalid colormap dimensions for .sfda file";
                }
            } else {
                qWarning() << "ProcessTab: No colormap found in Plot1 for .sfda file";
            }
        } else {
            qWarning() << "ProcessTab: No plottable data available in Plot1 for .sfda file";
        }
    }
    else {
        qWarning() << "ProcessTab: Unsupported file type:" << fileName;
    }

    qDebug() << "ProcessTab::onFileSelected completed";
}



QString ProcessTab::getCurrentSelectedFileName() const
{
    // 使用BaseTab的统一文件上下文管理
    return getCurrentFileContext();
}

void ProcessTab::initGlobalVariables()
{
    // 清除图例
    if (m_pPlot2 && m_pPlot2->legend) {
        m_pPlot2->legend->clearItems();
    }
    if (m_pPlot3 && m_pPlot3->legend) {
        m_pPlot3->legend->clearItems();
    }

    // 清除图形名称映射
    m_graphNameMap.clear();    

    horizontalLine1 = new QCPItemLine(m_pPlot1);
    horizontalLine1->setPen(QPen(Qt::red));
    horizontalLine1->setVisible(false);

    horizontalLine2 = new QCPItemLine(m_pPlot1);
    horizontalLine2->setPen(QPen(Qt::red));
    horizontalLine2->setVisible(false);

    verticalLine1 = new QCPItemLine(m_pPlot1);
    verticalLine1->setPen(QPen(Qt::red));
    verticalLine1->setVisible(false);

    verticalLine2 = new QCPItemLine(m_pPlot1);
    verticalLine2->setPen(QPen(Qt::red));
    verticalLine2->setVisible(false);
    disconnect(autoAlignValue, &QDoubleSpinBox::valueChanged, this, &ProcessTab::automaticAlignment);
    // Keep input field enabled
    autoAlignValue->setValue(0);

}

int ProcessTab::findUnifiedZeroPoint(const std::vector<std::vector<int>>& data) {
    std::vector<int> risingPoints;

    // 遍历每条曲线
    for (int x = 0; x < data.size(); ++x) {
        const std::vector<int>& curve = data[x];

        // 安全检查
        if (curve.empty()) {
            qWarning() << "Cannot process Zero_Data: curve at index" << x << "is empty";
            risingPoints.push_back(0); // 添加默认值以保持索引对齐
            continue;
        }

        int risingPoint = 0;
        int maxPeakIndex = -1;

        // 找到第一个既是最大值点又是极值点的位置
        for (size_t i = 1; i < curve.size() - 1; ++i) {
            if (curve[i] > curve[i - 1] && curve[i] > curve[i + 1]) {
                // 是极值点
                if (maxPeakIndex == -1) {
                    // 还没找到最大值点，当前点作为候选
                    maxPeakIndex = static_cast<int>(i);
                } else if (curve[i] > curve[maxPeakIndex]) {
                    // 当前极值点的值比之前记录的最大值点大，更新最大值点
                    maxPeakIndex = static_cast<int>(i);
                }
            }
        }

        if (maxPeakIndex != -1) {
            // 从最大值点往前回溯，找到不满足递减趋势的点
            for (int j = maxPeakIndex; j > 0; --j) {
                if (curve[j] < curve[j - 1]) {
                    // 不满足递减趋势，前一点即为时间零点
                    risingPoint = j;
                    break;
                }
            }
        }

        risingPoints.push_back(risingPoint);
    }

    // 安全检查
    if (risingPoints.empty()) {
        qWarning() << "Cannot process Zero_Data: risingPoints is empty";
        return 0;
    }

    // 返回最小的零点作为统一零点
    return *std::min_element(risingPoints.begin(), risingPoints.end());
}

void ProcessTab::alignData(std::vector<std::vector<int>>& data, int unifiedZero) {
    // 存储每条曲线的上升点
    std::vector<int> risingPoints;

    // 首先找到所有曲线的上升点
    for (int x = 0; x < data.size(); ++x) {
        const std::vector<int>& curve = data[x];

        // 安全检查
        if (curve.empty()) {
            risingPoints.push_back(0);
            continue;
        }

        int risingPoint = 0;
        int maxPeakIndex = -1;

        // 找到第一个既是最大值点又是极值点的位置
        for (size_t i = 1; i < curve.size() - 1; ++i) {
            if (curve[i] > curve[i - 1] && curve[i] > curve[i + 1]) {
                if (maxPeakIndex == -1) {
                    maxPeakIndex = static_cast<int>(i);
                } else if (curve[i] > curve[maxPeakIndex]) {
                    maxPeakIndex = static_cast<int>(i);
                }
            }
        }

        if (maxPeakIndex != -1) {
            for (int j = maxPeakIndex; j > 0; --j) {
                if (curve[j] < curve[j - 1]) {
                    risingPoint = j;
                    break;
                }
            }
        }

        risingPoints.push_back(risingPoint);
    }

    // 根据统一零点进行数据对齐
    for (int x = 0; x < data.size(); ++x) {
        // 安全检查
        if (x >= risingPoints.size()) {
            qWarning() << "Cannot process Zero_Data: index out of bounds for risingPoints";
            continue;
        }

        int shift = risingPoints[x] - unifiedZero;
        std::vector<int>& curve = data[x];

        // 安全检查
        if (curve.empty()) {
            continue;
        }

        if (shift > 0) {
            // 右移数据
            for (size_t i = 0; i < curve.size() - shift; ++i) {
                curve[i] = curve[i + shift];
            }
            // 填充末尾部分为0
            for (size_t i = curve.size() - shift; i < curve.size(); ++i) {
                curve[i] = 0;
            }
        } else if (shift < 0) {
            // 左移数据
            for (size_t i = -shift; i < curve.size(); ++i) {
                curve[i + shift] = curve[i];
            }
            // 填充开头部分为0
            for (size_t i = 0; i < -shift; ++i) {
                curve[i] = 0;
            }
        }
    }
}

void ProcessTab::setupPlots()
{
    // 调用基类的 setupPlots 方法初始化 m_pPlot1, m_pPlot2, m_pPlot3
    // 基类已经设置了标题和坐标轴标签
    BaseTab::setupPlots();

    // 十字线相关变量由基类管理

    // 初始化裁剪线对象
    if (horizontalLine1) {
        m_pPlot1->removeItem(horizontalLine1);
        horizontalLine1 = nullptr;
    }
    if (horizontalLine2) {
        m_pPlot1->removeItem(horizontalLine2);
        horizontalLine2 = nullptr;
    }
    if (verticalLine1) {
        m_pPlot1->removeItem(verticalLine1);
        verticalLine1 = nullptr;
    }
    if (verticalLine2) {
        m_pPlot1->removeItem(verticalLine2);
        verticalLine2 = nullptr;
    }

    // 重置手动对齐状态
    manualAlignmentSelected = false;
    // 不需要设置按钮样式，因为按钮在创建时已经有了默认样式

    // 为 m_pPlot2 (衰减曲线) 设置 legend
    if (m_pPlot2) {
        // 确保 legend 存在
        if (!m_pPlot2->legend) {
            m_pPlot2->legend = new QCPLegend;
        }

        // 配置 legend，但不强制设置可见性
        // 图例的可见性将由 CustomizePlot::applyDecayCurveStyle 方法根据图表是否有图形来决定
        m_pPlot2->legend->setFont(QFont("sans", 8));
        m_pPlot2->legend->setIconSize(QSize(20, 10));
        m_pPlot2->legend->setIconTextPadding(5);

        // 设置 legend 位置在右上角
        m_pPlot2->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
        m_pPlot2->axisRect()->insetLayout()->addElement(m_pPlot2->legend, Qt::AlignTop|Qt::AlignRight);

        // 设置 legend 可选择性
        m_pPlot2->legend->setSelectableParts(QCPLegend::spItems); // 使图例项可选择

        // 连接 legend 点击信号
        connect(m_pPlot2, &QCustomPlot::legendClick, this, &ProcessTab::onLegendClick);
    }

    // 为 m_pPlot3 (光谱曲线) 设置 legend
    if (m_pPlot3) {
        // 确保 legend 存在
        if (!m_pPlot3->legend) {
            m_pPlot3->legend = new QCPLegend;
        }

        // 配置 legend，但不强制设置可见性
        // 图例的可见性将由 CustomizePlot::applySpectralCurveStyle 方法根据图表是否有图形来决定
        m_pPlot3->legend->setFont(QFont("sans", 8));
        m_pPlot3->legend->setIconSize(QSize(20, 10));
        m_pPlot3->legend->setIconTextPadding(5);

        // 设置 legend 位置在右上角
        m_pPlot3->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
        m_pPlot3->axisRect()->insetLayout()->addElement(m_pPlot3->legend, Qt::AlignTop|Qt::AlignRight);

        // 设置 legend 可选择性
        m_pPlot3->legend->setSelectableParts(QCPLegend::spItems); // 使图例项可选择

        // 连接 legend 点击信号
        connect(m_pPlot3, &QCustomPlot::legendClick, this, &ProcessTab::onLegendClick);
    }

    // 使用基类的鼠标事件处理函数
    // 基类的setupPlots已经连接了基本的鼠标事件
}



void ProcessTab::enableAlignMode(bool enable)
{
    // 不再需要同步控制

    // Visual indication that align mode is active
    if (enable) {
        // Maybe change the border color of the plot or add some visual indicator
        m_pPlot1->setBackground(QBrush(QColor(240, 240, 255))); // Light blue background to indicate align mode
        m_pPlot1->applyThemeOnly(); // 只应用主题样式，不修改图形颜色
    } else {
        // Reset to normal appearance
        m_pPlot1->setBackground(Qt::white);
        m_pPlot1->applyThemeOnly(); // 只应用主题样式，不修改图形颜色
    }

    m_pPlot1->replot();
}

// 使用基类的 onCrosshairsToggled 实现

// 使用基类的 showHideCrossHairs 实现

// 使用基类的 onMousePress, onMouseMove, onMouseRelease 实现

// 重写基类的updateDecayCurvePlot方法
void ProcessTab::updateDecayCurvePlot(int x) {
    // 直接调用基类的方法处理基本功能
    BaseTab::updateDecayCurvePlot(x);

    // 收集 Plot2 数据并更新共享数据管理器
    if (m_pPlot2) {
        // 收集所有图形数据
        QVector<GraphData> decayCurveData;
        for (int i = 0; i < m_pPlot2->graphCount(); ++i) {
            QCPGraph* graph = m_pPlot2->graph(i);
            if (graph) {
                GraphData graphData(graph, DataSource::Process);
                decayCurveData.append(graphData);
            }
        }

        // 创建 Plot2 设置
        PlotSettings settings(m_pPlot2);

        // 更新共享数据
        SharedDataManager::getInstance()->updateDecayCurveData(decayCurveData, settings, TabType::ProcessTab);
    }
}

// 重写基类的updateSpectralCurvePlot方法
void ProcessTab::updateSpectralCurvePlot(int y) {
    // 直接调用基类的方法处理基本功能
    BaseTab::updateSpectralCurvePlot(y);

    // 收集 Plot3 数据并更新共享数据管理器
    if (m_pPlot3) {
        // 收集所有图形数据
        QVector<GraphData> spectralCurveData;
        for (int i = 0; i < m_pPlot3->graphCount(); ++i) {
            QCPGraph* graph = m_pPlot3->graph(i);
            if (graph) {
                GraphData graphData(graph, DataSource::Process);
                spectralCurveData.append(graphData);
            }
        }

        // 创建 Plot3 设置
        PlotSettings settings(m_pPlot3);

        // 更新共享数据
        SharedDataManager::getInstance()->updateSpectralCurveData(spectralCurveData, settings, TabType::ProcessTab);
    }
}

bool ProcessTab::eventFilter(QObject* watched, QEvent* event)
{
    // 首先调用基类的 eventFilter 方法处理基本功能
    if (BaseTab::eventFilter(watched, event)) {
        return true;
    }

    // 然后处理 ProcessTab 特有的功能
    if (event->type() == QEvent::Resize) {
        // 重新布局图例
        if (watched == m_pPlot2 && m_pPlot2->legend) {
            // 计算图例位置 - 右上角，留出一些边距
            // 不强制设置图例可见性，而是保持当前状态
            m_pPlot2->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
            m_pPlot2->replot();
        } else if (watched == m_pPlot3 && m_pPlot3->legend) {
            // 计算图例位置 - 右上角，留出一些边距
            // 不强制设置图例可见性，而是保持当前状态
            m_pPlot3->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
            m_pPlot3->replot();
        }
    }
    return false;
}

// 视频播放相关方法实现
void ProcessTab::onPlayPauseClicked()
{
    if (isPlaying) {
        // 当前正在播放，点击暂停
        playTimer->stop();
        playPauseButton->setText("▶");
        isPlaying = false;
        qDebug() << "Video playback paused";
    } else {
        // 当前暂停状态，点击开始播放
        if (allFramesData.isEmpty()) {
            // 如果没有加载帧数据，先加载
            loadAllFramesData();
        }

        if (!allFramesData.isEmpty()) {
            playTimer->start();
            playPauseButton->setText("⏸");
            isPlaying = true;
            qDebug() << "Video playback started with" << allFramesData.size() << "frames";
        } else {
            qWarning() << "No frame data available for playback";
        }
    }
}

void ProcessTab::onTimerTimeout()
{
    if (allFramesData.isEmpty()) {
        // 没有数据，停止播放
        onPlayPauseClicked();
        return;
    }

    // 更新当前帧到m_pPlot1
    if (currentFrameIndex < allFramesData.size()) {
        const auto& currentFrameData = allFramesData[currentFrameIndex];

        // 使用BaseTab的fillQCustomPlot方法更新m_pPlot1
        fillQCustomPlot(currentFrameData, false);

        qDebug() << "Playing frame" << currentFrameIndex + 1 << "of" << allFramesData.size();

        // 移动到下一帧
        currentFrameIndex++;

        // 如果到达最后一帧，循环播放
        if (currentFrameIndex >= allFramesData.size()) {
            currentFrameIndex = 0;
        }
    }
}

void ProcessTab::loadAllFramesData()
{
    // 清空之前的数据
    allFramesData.clear();
    currentFrameIndex = 0;

    // 获取当前工程文件路径
    QString currentFilePath = getCurrentFileContext();
    if (currentFilePath.isEmpty()) {
        qWarning() << "No project file selected for frame data loading";
        return;
    }

    QFileInfo fileInfo(currentFilePath);
    QString fileName = fileInfo.fileName();

    // 根据文件类型加载数据
    if (fileName.endsWith(".sfd")) {
        // 原始文件：使用 MeasureDataHandler
        ProjectFileManager* fileManager = ProjectFileManager::getInstance();
        MeasureDataHandler* handler = fileManager->getHandler(fileName);

        if (handler) {
            // 对于.sfd文件，我们只有一帧数据
            std::vector<std::vector<int>> frameData = handler->getDataArrayTotal();
            if (!frameData.empty() && !frameData[0].empty()) {
                allFramesData.append(frameData);
                qDebug() << "Loaded 1 frame from .sfd file";
            }
        } else {
            qWarning() << "No MeasureDataHandler found for .sfd file:" << fileName;
        }
    }
    else if (fileName.endsWith(".sfdp") || fileName.endsWith(".sfda")) {
        // 处理文件或分析文件：从当前显示的数据中提取
        if (m_pPlot1 && m_pPlot1->plottableCount() > 0) {
            QCPColorMap* colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (colorMap && colorMap->data()) {
                int keySize = colorMap->data()->keySize();
                int valueSize = colorMap->data()->valueSize();

                if (keySize > 0 && valueSize > 0) {
                    std::vector<std::vector<int>> frameData(keySize, std::vector<int>(valueSize));

                    for (int i = 0; i < keySize; ++i) {
                        for (int j = 0; j < valueSize; ++j) {
                            frameData[i][j] = static_cast<int>(colorMap->data()->cell(i, j));
                        }
                    }

                    allFramesData.append(frameData);
                    qDebug() << "Loaded 1 frame from" << fileName;
                }
            }
        }
    }

    // 如果只有一帧数据，我们可以创建一些变化来模拟视频效果
    // 这里可以根据实际需求进行调整
    if (allFramesData.size() == 1) {
        // 创建一些基于原始数据的变化帧
        const auto& originalFrame = allFramesData[0];

        // 创建几个变化帧（例如，通过调整亮度或添加噪声）
        for (int variation = 1; variation <= 10; ++variation) {
            std::vector<std::vector<int>> variationFrame = originalFrame;

            // 简单的亮度变化
            double factor = 0.8 + 0.4 * sin(variation * 0.6); // 0.8到1.2之间变化

            for (auto& row : variationFrame) {
                for (auto& value : row) {
                    value = static_cast<int>(value * factor);
                    if (value < 0) value = 0;
                }
            }

            allFramesData.append(variationFrame);
        }

        qDebug() << "Created" << allFramesData.size() << "variation frames for playback";
    }
}



