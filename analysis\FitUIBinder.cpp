#include "FitUIBinder.h"
#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>

FitUIBinder::FitUIBinder(QObject* parent)
    : QObject(parent),
    m_model(FitParameterModel::getInstance()) {

    // 连接模型信号
    connect(m_model, &FitParameterModel::parametersChanged, this, &FitUIBinder::onModelParametersChanged);
    connect(m_model, &FitParameterModel::parameterChanged, this, &FitUIBinder::onModelParameterChanged);
}

FitUIBinder::~FitUIBinder() {
    // 清理资源
}

void FitUIBinder::bindAnalysisMethod(QComboBox* comboBox) {
    if (!comboBox) return;

    m_analysisMethodComboBox = comboBox;

    // 设置初始值
    m_updatingUI = true;
    comboBox->setCurrentText(m_model->analysisMethod());
    m_updatingUI = false;

    // 连接信号
    connect(comboBox, &QComboBox::currentTextChanged, this, &FitUIBinder::onAnalysisMethodChanged);
    connect(m_model, &FitParameterModel::analysisMethodChanged, [this](const QString& method) {
        if (m_analysisMethodComboBox && !m_updatingUI) {
            m_updatingUI = true;
            m_analysisMethodComboBox->setCurrentText(method);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindFittingModel(QComboBox* comboBox) {
    if (!comboBox) return;

    m_fittingModelComboBox = comboBox;

    // 设置初始值
    m_updatingUI = true;
    comboBox->setCurrentText(m_model->fittingModel());
    m_updatingUI = false;

    // 连接信号
    connect(comboBox, &QComboBox::currentTextChanged, this, &FitUIBinder::onFittingModelChanged);
    connect(m_model, &FitParameterModel::fittingModelChanged, [this](const QString& model) {
        if (m_fittingModelComboBox && !m_updatingUI) {
            m_updatingUI = true;
            m_fittingModelComboBox->setCurrentText(model);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindIRFParameters(QDoubleSpinBox* t0SpinBox, QDoubleSpinBox* fwhmSpinBox) {
    if (!t0SpinBox || !fwhmSpinBox) return;

    m_t0SpinBox = t0SpinBox;
    m_fwhmSpinBox = fwhmSpinBox;

    // 设置初始值
    m_updatingUI = true;
    t0SpinBox->setValue(m_model->t0());
    fwhmSpinBox->setValue(m_model->fwhm());
    m_updatingUI = false;

    // 连接信号
    connect(t0SpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &FitUIBinder::onT0Changed);
    connect(fwhmSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &FitUIBinder::onFwhmChanged);

    connect(m_model, &FitParameterModel::t0Changed, [this](double value) {
        if (m_t0SpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_t0SpinBox->setValue(value);
            m_updatingUI = false;
        }
    });

    connect(m_model, &FitParameterModel::fwhmChanged, [this](double value) {
        if (m_fwhmSpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_fwhmSpinBox->setValue(value);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindAnalysisMode(QComboBox* comboBox) {
    if (!comboBox) return;

    m_analysisModeComboBox = comboBox;

    // 设置初始值
    m_updatingUI = true;
    comboBox->setCurrentText(m_model->analysisMode());
    m_updatingUI = false;

    // 连接信号
    connect(comboBox, &QComboBox::currentTextChanged, this, &FitUIBinder::onAnalysisModeChanged);
    connect(m_model, &FitParameterModel::analysisModeChanged, [this](const QString& mode) {
        if (m_analysisModeComboBox && !m_updatingUI) {
            m_updatingUI = true;
            m_analysisModeComboBox->setCurrentText(mode);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindExponentialModel(QComboBox* comboBox) {
    if (!comboBox) return;

    m_exponentialModelComboBox = comboBox;

    // 设置初始值
    m_updatingUI = true;

    // Translate internal model name to UI model name for initial setup
    QString modelName = m_model->exponentialModel();
    if (modelName == "N指数衰减") {
        comboBox->setCurrentText("Exponential");
    } else {
        comboBox->setCurrentText(modelName);
    }

    m_updatingUI = false;

    // 连接信号
    connect(comboBox, &QComboBox::currentTextChanged, this, &FitUIBinder::onExponentialModelChanged);
    connect(m_model, &FitParameterModel::exponentialModelChanged, [this](const QString& model) {
        if (m_exponentialModelComboBox && !m_updatingUI) {
            m_updatingUI = true;

            // Translate internal model names to UI model names
            if (model == "N指数衰减") {
                // Map "N指数衰减" to "Exponential" for UI
                m_exponentialModelComboBox->setCurrentText("Exponential");
            } else {
                // Keep other model names as is (e.g., "Stretched Exponential")
                m_exponentialModelComboBox->setCurrentText(model);
            }

            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindModelParametersCount(QSpinBox* spinBox) {
    if (!spinBox) return;

    m_modelParamsSpinBox = spinBox;

    // 设置初始值
    m_updatingUI = true;
    spinBox->setValue(m_model->modelParametersCount());
    m_updatingUI = false;

    // 连接信号
    connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &FitUIBinder::onModelParametersCountChanged);
    // connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, [this](int count) {
    //     if (!m_updatingUI) {
    //         m_updatingUI = true;
    //         m_model->setModelParametersCount(count);
    //         m_updatingUI = false;
    //     }
    // });

    connect(m_model, &FitParameterModel::modelParametersCountChanged, [this](int count) {
        if (m_modelParamsSpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_modelParamsSpinBox->setValue(count);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindModelAlgorithm(QComboBox* comboBox) {
    if (!comboBox) return;

    m_modelAlgorithmComboBox = comboBox;

    // 设置初始值
    m_updatingUI = true;
    comboBox->setCurrentText(m_model->modelAlgorithm());
    m_updatingUI = false;

    // 连接信号
    connect(comboBox, &QComboBox::currentTextChanged, this, &FitUIBinder::onModelAlgorithmChanged);
    connect(m_model, &FitParameterModel::modelAlgorithmChanged, [this](const QString& algorithm) {
        if (m_modelAlgorithmComboBox && !m_updatingUI) {
            m_updatingUI = true;
            m_modelAlgorithmComboBox->setCurrentText(algorithm);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindIterations(QSpinBox* spinBox) {
    if (!spinBox) return;

    m_iterationsSpinBox = spinBox;

    // 设置初始值
    m_updatingUI = true;
    spinBox->setValue(100000);
    // 同时更新模型中的值
    m_model->setIterations(100000);
    qDebug() << "初始化迭代次数为:" << 100000;
    m_updatingUI = false;

    // 连接信号
    connect(spinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &FitUIBinder::onIterationsChanged);
    connect(m_model, &FitParameterModel::iterationsChanged, [this](int count) {
        if (m_iterationsSpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_iterationsSpinBox->setValue(count);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindFittingRange(QDoubleSpinBox* fromSpinBox, QDoubleSpinBox* toSpinBox) {
    if (!fromSpinBox || !toSpinBox) return;

    m_rangeFromSpinBox = fromSpinBox;
    m_rangeToSpinBox = toSpinBox;

    // 设置初始值
    m_updatingUI = true;
    fromSpinBox->setValue(m_model->rangeFrom());
    toSpinBox->setValue(m_model->rangeTo());
    m_updatingUI = false;

    // 连接信号
    connect(fromSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &FitUIBinder::onRangeFromChanged);
    connect(toSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &FitUIBinder::onRangeToChanged);

    connect(m_model, &FitParameterModel::rangeFromChanged, [this](double value) {
        if (m_rangeFromSpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_rangeFromSpinBox->setValue(value);
            m_updatingUI = false;
        }
    });

    connect(m_model, &FitParameterModel::rangeToChanged, [this](double value) {
        if (m_rangeToSpinBox && !m_updatingUI) {
            m_updatingUI = true;
            m_rangeToSpinBox->setValue(value);
            m_updatingUI = false;
        }
    });
}

void FitUIBinder::bindParameterTable(QTableWidget* tableWidget) {
    if (!tableWidget) return;

    m_parameterTable = tableWidget;

    // 设置表格属性
    tableWidget->setColumnCount(3);
    tableWidget->setHorizontalHeaderLabels(QStringList() << "Parameter" << "Value" << "Fix");

    // 更新参数表格
    updateParameterTable();
}

void FitUIBinder::updateParameterTable() {
    if (!m_parameterTable) return;

    // 标记正在更新UI
    m_updatingUI = true;

    // 断开所有信号连接
    m_parameterTable->blockSignals(true);

    // 获取参数列表
    QVector<FitParameterModel::ParameterInfo> params = m_model->getParameters();

    // 设置行数
    m_parameterTable->setRowCount(params.size());

    // 填充参数表格
    for (int i = 0; i < params.size(); ++i) {
        setupParameterRow(i, params[i]);
    }

    // 恢复信号连接
    m_parameterTable->blockSignals(false);

    // 取消标记
    m_updatingUI = false;
}

void FitUIBinder::setupParameterRow(int row, const FitParameterModel::ParameterInfo& param) {
    if (!m_parameterTable) return;

    // 参数名称和限制按钮
    QWidget* cellWidget = new QWidget();
    QHBoxLayout* layout = new QHBoxLayout(cellWidget);
    layout->setContentsMargins(0, 0, 0, 0);

    QLabel* nameLabel = new QLabel(param.displayName);
    layout->addWidget(nameLabel);

    QPushButton* limitsButton = new QPushButton("⇔");
    limitsButton->setFixedWidth(50);
    limitsButton->setProperty("paramName", param.name);

    // 设置限制按钮的样式
    if (param.hasMinConstraint || param.hasMaxConstraint) {
        limitsButton->setStyleSheet("background-color: #FF6666;");
    } else {
        limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
    }

    layout->addWidget(limitsButton);
    layout->setAlignment(limitsButton, Qt::AlignRight);

    m_parameterTable->setCellWidget(row, 0, cellWidget);

    // 参数值
    QDoubleSpinBox* valueSpinBox = new QDoubleSpinBox();
    valueSpinBox->setMinimum(-1000000.0);
    valueSpinBox->setMaximum(1000000.0);
    valueSpinBox->setValue(param.value);
    valueSpinBox->setAlignment(Qt::AlignCenter);
    valueSpinBox->setProperty("paramName", param.name);

    m_parameterTable->setCellWidget(row, 1, valueSpinBox);

    // 固定复选框
    QWidget* fixButtonWidget = new QWidget();
    QHBoxLayout* fixButtonLayout = new QHBoxLayout(fixButtonWidget);
    fixButtonLayout->setContentsMargins(0, 0, 0, 0);

    QPushButton* fixButton = new QPushButton(!param.fixed ? "✓" : "");
    fixButton->setCheckable(true);
    fixButton->setChecked(!param.fixed);
    fixButton->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");
    fixButton->setProperty("paramName", param.name);

    fixButtonLayout->addWidget(fixButton);
    fixButtonLayout->setAlignment(Qt::AlignCenter);

    m_parameterTable->setCellWidget(row, 2, fixButtonWidget);

    // 连接信号
    connect(valueSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &FitUIBinder::onParameterValueChanged);
    connect(fixButton, &QPushButton::clicked, [this, fixButton]() {
        QString paramName = fixButton->property("paramName").toString();
        bool checked = fixButton->isChecked();
        fixButton->setText(checked ? "✓" : "");
        if (!m_updatingUI) {
            m_model->setParameterFixed(paramName, !checked);
        }
    });

    connect(limitsButton, &QPushButton::clicked, [this, limitsButton, param]() {
        // 创建参数范围设置对话框
        QWidget* popupWidget = new QWidget();
        popupWidget->setWindowFlags(Qt::Popup);
        QHBoxLayout* popupLayout = new QHBoxLayout(popupWidget);
        popupLayout->setContentsMargins(5, 5, 5, 5);

        // Min部分
        QLabel* minLabel = new QLabel("Min:");
        popupLayout->addWidget(minLabel);

        QPushButton* minCheckBox = new QPushButton(param.hasMinConstraint ? "✓" : "");
        minCheckBox->setCheckable(true);
        minCheckBox->setChecked(param.hasMinConstraint);
        minCheckBox->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");
        popupLayout->addWidget(minCheckBox);

        QDoubleSpinBox* minSpinBox = new QDoubleSpinBox();
        minSpinBox->setDecimals(4);
        minSpinBox->setValue(param.minValue);
        minSpinBox->setMinimum(-1000000.0);
        minSpinBox->setMaximum(1000000.0);
        minSpinBox->setEnabled(param.hasMinConstraint);
        popupLayout->addWidget(minSpinBox);

        // Max部分
        QLabel* maxLabel = new QLabel("Max:");
        popupLayout->addWidget(maxLabel);

        QPushButton* maxCheckBox = new QPushButton(param.hasMaxConstraint ? "✓" : "");
        maxCheckBox->setCheckable(true);
        maxCheckBox->setChecked(param.hasMaxConstraint);
        maxCheckBox->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");
        popupLayout->addWidget(maxCheckBox);

        QDoubleSpinBox* maxSpinBox = new QDoubleSpinBox();
        maxSpinBox->setDecimals(4);
        maxSpinBox->setValue(param.maxValue);
        maxSpinBox->setMinimum(-1000000.0);
        maxSpinBox->setMaximum(1000000.0);
        maxSpinBox->setEnabled(param.hasMaxConstraint);
        popupLayout->addWidget(maxSpinBox);

        // Close按钮
        QPushButton* closeButton = new QPushButton("Close");
        popupLayout->addWidget(closeButton);

        // 连接信号槽
        connect(minCheckBox, &QPushButton::clicked, [this, minCheckBox, minSpinBox, limitsButton, maxCheckBox, maxSpinBox, param]() {
            bool checked = minCheckBox->isChecked();
            minCheckBox->setText(checked ? "✓" : "");
            minSpinBox->setEnabled(checked);

            // 设置⇔按钮的背景色
            if (checked || maxCheckBox->isChecked()) {
                limitsButton->setStyleSheet("background-color: #FF6666;");
            } else {
                limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
            }

            // 更新模型
            if (!m_updatingUI) {
                m_model->setParameterRange(param.name, checked, minSpinBox->value(),
                                           maxCheckBox->isChecked(), maxSpinBox->value());
            }
        });

        connect(maxCheckBox, &QPushButton::clicked, [this, maxCheckBox, maxSpinBox, limitsButton, minCheckBox, minSpinBox, param]() {
            bool checked = maxCheckBox->isChecked();
            maxCheckBox->setText(checked ? "✓" : "");
            maxSpinBox->setEnabled(checked);

            // 设置⇔按钮的背景色
            if (minCheckBox->isChecked() || checked) {
                limitsButton->setStyleSheet("background-color: #FF6666;");
            } else {
                limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
            }

            // 更新模型
            if (!m_updatingUI) {
                m_model->setParameterRange(param.name, minCheckBox->isChecked(), minSpinBox->value(),
                                           checked, maxSpinBox->value());
            }
        });

        connect(minSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), [this, minCheckBox, maxCheckBox, minSpinBox, maxSpinBox, param]() {
            // 更新模型
            if (!m_updatingUI && minCheckBox->isChecked()) {
                m_model->setParameterRange(param.name, true, minSpinBox->value(),
                                           maxCheckBox->isChecked(), maxSpinBox->value());
            }
        });

        connect(maxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), [this, maxCheckBox, minCheckBox, minSpinBox, maxSpinBox, param]() {
            // 更新模型
            if (!m_updatingUI && maxCheckBox->isChecked()) {
                m_model->setParameterRange(param.name, minCheckBox->isChecked(), minSpinBox->value(),
                                           true, maxSpinBox->value());
            }
        });

        connect(closeButton, &QPushButton::clicked, popupWidget, &QWidget::close);

        // 显示弹出框
        QPoint pos = limitsButton->mapToGlobal(QPoint(limitsButton->width(), 0));
        popupWidget->move(pos);
        popupWidget->show();
    });
}

QString FitUIBinder::getParameterNameFromRow(int row) const {
    if (!m_parameterTable || row < 0 || row >= m_parameterTable->rowCount()) {
        return QString();
    }

    QWidget* cellWidget = m_parameterTable->cellWidget(row, 0);
    if (!cellWidget) {
        return QString();
    }

    QLabel* label = cellWidget->findChild<QLabel*>();
    if (!label) {
        return QString();
    }

    QString displayName = label->text();
    QStringList parts = displayName.split(" ");
    if (parts.isEmpty()) {
        return QString();
    }

    return parts[0];
}

void FitUIBinder::onAnalysisMethodChanged(const QString& text) {
    if (!m_updatingUI) {
        m_model->setAnalysisMethod(text);
    }
}

void FitUIBinder::onFittingModelChanged(const QString& text) {
    if (!m_updatingUI) {
        m_model->setFittingModel(text);
    }
}

void FitUIBinder::onT0Changed(double value) {
    if (!m_updatingUI) {
        m_model->setT0(value);
    }
}

void FitUIBinder::onFwhmChanged(double value) {
    if (!m_updatingUI) {
        m_model->setFwhm(value);
    }
}

void FitUIBinder::onAnalysisModeChanged(const QString& text) {
    if (!m_updatingUI) {
        m_model->setAnalysisMode(text);
    }
}

void FitUIBinder::onExponentialModelChanged(const QString& text) {
    if (!m_updatingUI) {
        // Translate UI model names to internal model names
        if (text == "Exponential") {
            // Map "Exponential" to "N指数衰减" for internal model
            m_model->setExponentialModel("N指数衰减");
        } else {
            // Keep other model names as is (e.g., "Stretched Exponential")
            m_model->setExponentialModel(text);
        }
    }
}

void FitUIBinder::onModelParametersCountChanged(int value) {
    if (!m_updatingUI) {
        m_model->setModelParametersCount(value);
    }
}

void FitUIBinder::onModelAlgorithmChanged(const QString& text) {
    if (!m_updatingUI) {
        m_model->setModelAlgorithm(text);
    }
}

void FitUIBinder::onIterationsChanged(int value) {
    if (!m_updatingUI) {
        m_model->setIterations(value);
    }
}

void FitUIBinder::onRangeFromChanged(double value) {
    if (!m_updatingUI) {
        m_model->setRangeFrom(value);
    }
}

void FitUIBinder::onRangeToChanged(double value) {
    if (!m_updatingUI) {
        m_model->setRangeTo(value);
    }
}

void FitUIBinder::onParameterValueChanged(double value) {
    if (m_updatingUI) return;

    QDoubleSpinBox* spinBox = qobject_cast<QDoubleSpinBox*>(sender());
    if (!spinBox) return;

    QString paramName = spinBox->property("paramName").toString();
    if (!paramName.isEmpty()) {
        m_model->setParameterValue(paramName, value);
    }
}

void FitUIBinder::onParameterFixedChanged(bool checked) {
    if (m_updatingUI) return;

    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    QString paramName = button->property("paramName").toString();
    if (!paramName.isEmpty()) {
        m_model->setParameterFixed(paramName, checked);
    }
}

void FitUIBinder::onParameterLimitsClicked() {
    // 这个函数在setupParameterRow中直接使用lambda实现了
}

void FitUIBinder::onModelParametersChanged() {
    // 更新参数表格
    updateParameterTable();
}

void FitUIBinder::onModelParameterChanged(const QString& name) {
    if (!m_parameterTable || m_updatingUI) return;

    // 查找参数所在的行
    for (int row = 0; row < m_parameterTable->rowCount(); ++row) {
        QWidget* cellWidget = m_parameterTable->cellWidget(row, 0);
        if (!cellWidget) continue;

        QLabel* label = cellWidget->findChild<QLabel*>();
        if (!label) continue;

        QString displayName = label->text();
        QStringList parts = displayName.split(" ");
        if (parts.isEmpty()) continue;

        if (parts[0] == name) {
            // 找到参数所在行，更新UI
            FitParameterModel::ParameterInfo param = m_model->getParameter(name);

            // 更新值
            QDoubleSpinBox* valueSpinBox = qobject_cast<QDoubleSpinBox*>(m_parameterTable->cellWidget(row, 1));
            if (valueSpinBox) {
                m_updatingUI = true;
                valueSpinBox->setValue(param.value);
                m_updatingUI = false;
            }

            // 更新固定状态
            QWidget* fixWidget = m_parameterTable->cellWidget(row, 2);
            if (fixWidget) {
                QPushButton* fixButton = fixWidget->findChild<QPushButton*>();
                if (fixButton) {
                    m_updatingUI = true;
                    fixButton->setChecked(!param.fixed);
                    fixButton->setText(!param.fixed ? "✓" : "");
                    m_updatingUI = false;
                }
            }

            // 更新限制按钮样式
            QPushButton* limitsButton = cellWidget->findChild<QPushButton*>();
            if (limitsButton) {
                if (param.hasMinConstraint || param.hasMaxConstraint) {
                    limitsButton->setStyleSheet("background-color: #FF6666;");
                } else {
                    limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
                }
            }

            break;
        }
    }
}
